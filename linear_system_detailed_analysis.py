#!/usr/bin/env python3
"""
详细分析线性方程系统的构建过程

重要问题：
1. 线性方程的左右都是什么？
2. 怎么计算来的，用了哪些写？
3. 公式是否正确？
4. EXP mapping和LOG mapping是否需要？
5. Transpose等操作是否遗漏？
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class LinearSystemDetailedAnalyzer:
    """详细分析线性方程系统构建过程"""
    
    def __init__(self):
        # 基于论文的统一位姿表示公式
        self.pose_representation_formulas = {
            'unified_pose': {
                'formula': 'ξ1 ⊕ ξ2 = -Log(R1 R2), t1 + R1 t2',
                'inverse': 'ξ1 ⊖ ξ2 = -Log(R2^T R1), R2^T(t1 - t2)',
                'exp_mapping': 'R = Exp(φ), where φ ∈ so(n)',
                'log_mapping': 'φ = Log(R), where R ∈ SO(n)',
                'operations_needed': [
                    'Exp(φ1) → R1',
                    'Exp(φ2) → R2', 
                    'R1 * R2 → matrix multiplication',
                    'R2^T → transpose',
                    'Log(R2^T R1) → log mapping',
                    'R2^T * (t1 - t2) → matrix-vector multiplication'
                ]
            }
        }
        
        # 线性方程系统 Ax = b 的详细构建过程
        self.linear_system_construction = {
            'gauss_newton': {
                'description': 'Gauss-Newton方法求解非线性最小二乘',
                'system': 'H * Δx = -g',
                'left_side': {
                    'H': 'Hessian近似矩阵',
                    'formula': 'H = J^T * Ω * J',
                    'components': {
                        'J': 'Jacobian矩阵 ∂e/∂x',
                        'J^T': 'Jacobian转置',
                        'Ω': '信息矩阵 (协方差逆)',
                        'operations': [
                            ('Compute_Jacobian', 'J = ∂e/∂x', 'per_factor'),
                            ('Transpose', 'J^T', 'per_factor'),
                            ('StaticVMM', 'J^T * Ω', 'per_factor'),
                            ('StaticVMM', '(J^T * Ω) * J', 'per_factor'),
                            ('Accumulate', 'H += J^T * Ω * J', 'per_factor')
                        ]
                    }
                },
                'right_side': {
                    'g': '梯度向量',
                    'formula': 'g = J^T * Ω * e',
                    'components': {
                        'e': '误差向量',
                        'operations': [
                            ('Compute_Error', 'e = f(x)', 'per_factor'),
                            ('StaticVMM', 'J^T * Ω', 'per_factor'),
                            ('StaticVMM', '(J^T * Ω) * e', 'per_factor'),
                            ('Accumulate', 'g += J^T * Ω * e', 'per_factor')
                        ]
                    }
                },
                'solution': {
                    'method': 'Cholesky分解或稀疏求解',
                    'operations': [
                        ('Cholesky', 'H = L * L^T', 'once_per_iteration'),
                        ('Forward_Sub', 'L * y = -g', 'once_per_iteration'),
                        ('Backward_Sub', 'L^T * Δx = y', 'once_per_iteration')
                    ]
                }
            }
        }
        
        # 具体因子的Jacobian计算（需要EXP/LOG mapping）
        self.factor_jacobian_details = {
            'pose_pose_factor': {
                'error_function': 'e = Log(R_ij^{-1} * R_i^{-1} * R_j)',
                'jacobian_computation': [
                    ('Exp', 'R_i = Exp(φ_i)', 'RRAM_write'),
                    ('Exp', 'R_j = Exp(φ_j)', 'RRAM_write'),
                    ('Transpose', 'R_i^T', 'RRAM_write'),
                    ('StaticVMM', 'R_i^T * R_j', 'RRAM_write'),
                    ('StaticVMM', 'R_ij^T * (R_i^T * R_j)', 'RRAM_write'),
                    ('Log', 'Log(result)', 'RRAM_write'),
                    ('Jacobian_Right', '∂Log(R)/∂φ', 'RRAM_write'),
                    ('Jacobian_Left', '∂(R_i^T * R_j)/∂φ_i', 'RRAM_write'),
                    ('Jacobian_Left', '∂(R_i^T * R_j)/∂φ_j', 'RRAM_write')
                ],
                'writes_per_factor': 'depends_on_manifold_dim'
            },
            
            'pose_landmark_factor': {
                'error_function': 'e = h(T_i * p_j) - z_ij',
                'jacobian_computation': [
                    ('Exp', 'T_i = Exp(ξ_i)', 'RRAM_write'),
                    ('Transform', 'T_i * p_j', 'RRAM_write'),
                    ('Project', 'h(T_i * p_j)', 'RRAM_write'),
                    ('VP', 'e = h(...) - z_ij', 'RRAM_write'),
                    ('Jacobian_SE3', '∂h/∂ξ_i', 'RRAM_write'),
                    ('Jacobian_Point', '∂h/∂p_j', 'RRAM_write')
                ],
                'writes_per_factor': 'depends_on_observation_dim'
            }
        }
        
        # 基于表格的变量维度
        self.table_variable_dimensions = {
            'robot_localization': 3,
            'manipulator_localization': 2,
            'autovehicle_localization': 3,
            'quadrotor_localization': 6,
        }
    
    def analyze_linear_system_construction(self, app_name: str, algorithm: str):
        """详细分析线性系统构建过程"""
        
        print(f"\n🔍 详细分析线性系统构建: {app_name} - {algorithm}")
        print("=" * 80)
        
        # 获取因子图结构
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        algorithm_key = f'{app_name}_{algorithm}'
        variable_dim = self.table_variable_dimensions.get(algorithm_key, 0)
        num_factors = len(factors)
        
        print(f"📊 系统信息:")
        print(f"   变量维度: {variable_dim}")
        print(f"   因子数量: {num_factors}")
        print(f"   Hessian矩阵大小: {variable_dim}×{variable_dim}")
        print(f"   梯度向量大小: {variable_dim}")
        print()
        
        # 分析线性系统 H * Δx = -g
        self._analyze_hessian_construction(variable_dim, num_factors)
        self._analyze_gradient_construction(variable_dim, num_factors)
        self._analyze_system_solution(variable_dim)
        self._analyze_missing_operations(variable_dim, num_factors)
        
        return self._calculate_total_writes(variable_dim, num_factors)
    
    def _analyze_hessian_construction(self, variable_dim: int, num_factors: int):
        """分析Hessian矩阵 H = J^T * Ω * J 的构建"""
        
        print("📋 Hessian矩阵构建: H = J^T * Ω * J")
        print("-" * 60)
        
        # 假设每个因子的误差维度
        if variable_dim <= 3:
            error_dim = 2  # 2D观测
        else:
            error_dim = 3  # 3D观测
        
        print(f"每个因子的操作:")
        print(f"1️⃣ 计算Jacobian: J ({error_dim}×{variable_dim})")
        
        # Jacobian计算需要的操作
        jacobian_ops = self._get_jacobian_operations(variable_dim, error_dim)
        jacobian_writes = sum(op[2] for op in jacobian_ops if op[2] != 'SIMD')
        
        for op_type, description, writes in jacobian_ops:
            status = "RRAM写入" if writes != 'SIMD' else "SIMD计算"
            print(f"   {op_type}: {description} - {writes} ({status})")
        
        print(f"   Jacobian计算总写入: {jacobian_writes}")
        print()
        
        print(f"2️⃣ 计算J^T:")
        transpose_writes = error_dim * variable_dim
        print(f"   Transpose: J^T ({variable_dim}×{error_dim}) - {transpose_writes} 写入")
        print()
        
        print(f"3️⃣ 计算J^T * Ω:")
        jt_omega_writes = variable_dim * error_dim
        print(f"   StaticVMM: J^T * Ω - {jt_omega_writes} 写入")
        print()
        
        print(f"4️⃣ 计算(J^T * Ω) * J:")
        jt_omega_j_writes = variable_dim * variable_dim
        print(f"   StaticVMM: (J^T * Ω) * J - {jt_omega_j_writes} 写入")
        print()
        
        print(f"5️⃣ 累加到Hessian:")
        accumulate_writes = variable_dim * variable_dim
        print(f"   Accumulate: H += result - {accumulate_writes} 写入")
        print()
        
        hessian_writes_per_factor = (jacobian_writes + transpose_writes + 
                                   jt_omega_writes + jt_omega_j_writes + accumulate_writes)
        total_hessian_writes = hessian_writes_per_factor * num_factors
        
        print(f"🎯 Hessian构建总结:")
        print(f"   每因子写入: {hessian_writes_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   Hessian总写入: {total_hessian_writes:,}")
        print()
    
    def _analyze_gradient_construction(self, variable_dim: int, num_factors: int):
        """分析梯度向量 g = J^T * Ω * e 的构建"""
        
        print("📋 梯度向量构建: g = J^T * Ω * e")
        print("-" * 60)
        
        if variable_dim <= 3:
            error_dim = 2
        else:
            error_dim = 3
        
        print(f"每个因子的操作:")
        print(f"1️⃣ 计算误差: e ({error_dim}维)")
        
        # 误差计算需要的操作（包括EXP/LOG mapping）
        error_ops = self._get_error_operations(variable_dim, error_dim)
        error_writes = sum(op[2] for op in error_ops if op[2] != 'SIMD')
        
        for op_type, description, writes in error_ops:
            status = "RRAM写入" if writes != 'SIMD' else "SIMD计算"
            print(f"   {op_type}: {description} - {writes} ({status})")
        
        print(f"   误差计算总写入: {error_writes}")
        print()
        
        print(f"2️⃣ 计算J^T * Ω * e:")
        jt_omega_e_writes = variable_dim
        print(f"   StaticVMM: J^T * Ω * e - {jt_omega_e_writes} 写入")
        print()
        
        print(f"3️⃣ 累加到梯度:")
        accumulate_writes = variable_dim
        print(f"   Accumulate: g += result - {accumulate_writes} 写入")
        print()
        
        gradient_writes_per_factor = error_writes + jt_omega_e_writes + accumulate_writes
        total_gradient_writes = gradient_writes_per_factor * num_factors
        
        print(f"🎯 梯度构建总结:")
        print(f"   每因子写入: {gradient_writes_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   梯度总写入: {total_gradient_writes:,}")
        print()
    
    def _analyze_system_solution(self, variable_dim: int):
        """分析线性系统求解 H * Δx = -g"""
        
        print("📋 线性系统求解: H * Δx = -g")
        print("-" * 60)
        
        print("1️⃣ Cholesky分解: H = L * L^T")
        cholesky_writes = (variable_dim * variable_dim * variable_dim) // 6  # 约1/6 n³
        print(f"   Cholesky分解写入: {cholesky_writes}")
        print()
        
        print("2️⃣ 前向替换: L * y = -g")
        forward_writes = variable_dim * variable_dim // 2
        print(f"   前向替换写入: {forward_writes}")
        print()
        
        print("3️⃣ 后向替换: L^T * Δx = y")
        backward_writes = variable_dim * variable_dim // 2
        print(f"   后向替换写入: {backward_writes}")
        print()
        
        total_solution_writes = cholesky_writes + forward_writes + backward_writes
        print(f"🎯 系统求解总写入: {total_solution_writes}")
        print()
    
    def _analyze_missing_operations(self, variable_dim: int, num_factors: int):
        """分析之前遗漏的关键操作"""
        
        print("📋 之前遗漏的关键操作")
        print("-" * 60)
        
        print("🔍 EXP/LOG Mapping操作:")
        exp_operations = num_factors * 2  # 每因子至少2个位姿
        log_operations = num_factors * 1  # 每因子1个误差计算
        print(f"   Exp mapping: {exp_operations} 次 (R = Exp(φ))")
        print(f"   Log mapping: {log_operations} 次 (φ = Log(R))")
        print()
        
        print("🔍 Transpose操作:")
        transpose_operations = num_factors * 3  # Jacobian转置 + 旋转矩阵转置
        print(f"   矩阵转置: {transpose_operations} 次")
        print()
        
        print("🔍 流形上的Jacobian计算:")
        manifold_jacobian = num_factors * variable_dim * 2  # 左右Jacobian
        print(f"   流形Jacobian: {manifold_jacobian} 次写入")
        print()
        
        total_missing = (exp_operations * 9 + log_operations * 3 + 
                        transpose_operations * variable_dim * variable_dim + 
                        manifold_jacobian)
        
        print(f"🎯 遗漏操作总写入: {total_missing:,}")
        print()
    
    def _get_jacobian_operations(self, variable_dim: int, error_dim: int):
        """获取Jacobian计算的具体操作"""
        
        return [
            ('Exp', f'R = Exp(φ) for poses', 9),
            ('Transform', 'Apply transformation', variable_dim),
            ('Project', 'Observation model h(x)', error_dim),
            ('Jacobian_SE3', '∂h/∂ξ (SE3 Jacobian)', error_dim * 6),
            ('Jacobian_Point', '∂h/∂p (Point Jacobian)', error_dim * 3),
            ('Chain_Rule', 'Chain rule application', 'SIMD')
        ]
    
    def _get_error_operations(self, variable_dim: int, error_dim: int):
        """获取误差计算的具体操作"""
        
        return [
            ('Exp', 'R_i = Exp(φ_i)', 9),
            ('Exp', 'R_j = Exp(φ_j)', 9),
            ('Transpose', 'R_i^T', 9),
            ('StaticVMM', 'R_i^T * R_j', 9),
            ('Log', 'Log(R_i^T * R_j)', 3),
            ('VP', 'Translation error', 3),
            ('Combine', 'Combine rotation and translation', error_dim)
        ]
    
    def _calculate_total_writes(self, variable_dim: int, num_factors: int):
        """计算总的RRAM写入次数"""
        
        # 基于详细分析的估算
        if variable_dim <= 3:
            error_dim = 2
        else:
            error_dim = 3
            
        # Hessian构建
        jacobian_writes = 50  # 基于Jacobian操作估算
        hessian_per_factor = jacobian_writes + error_dim * variable_dim * 3
        total_hessian = hessian_per_factor * num_factors
        
        # 梯度构建
        error_writes = 42  # 基于误差操作估算
        gradient_per_factor = error_writes + variable_dim * 2
        total_gradient = gradient_per_factor * num_factors
        
        # 系统求解
        total_solution = (variable_dim ** 3) // 6 + variable_dim ** 2
        
        # 遗漏操作
        total_missing = num_factors * (18 + 3 + variable_dim ** 2 + variable_dim * 2)
        
        total_writes = total_hessian + total_gradient + total_solution + total_missing
        
        print(f"🎯 总RRAM写入统计:")
        print(f"   Hessian构建: {total_hessian:,}")
        print(f"   梯度构建: {total_gradient:,}")
        print(f"   系统求解: {total_solution:,}")
        print(f"   遗漏操作: {total_missing:,}")
        print(f"   总计: {total_writes:,}")
        print()
        
        return total_writes
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()


def main():
    """主函数 - 详细分析线性系统构建"""
    
    analyzer = LinearSystemDetailedAnalyzer()
    
    print("🚀 详细分析线性方程系统构建过程")
    print("=" * 80)
    print("关键问题:")
    print("1. 线性方程 H * Δx = -g 的左右都是什么？")
    print("2. 怎么计算来的，用了哪些RRAM写入？")
    print("3. EXP/LOG mapping和Transpose是否需要？")
    print("4. 公式是否正确？")
    print()
    
    # 分析定位算法的线性系统构建
    localization_algorithms = [
        ('robot', 'localization'),
        ('manipulator', 'localization'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]
    
    total_all_writes = 0
    
    for app_name, algorithm in localization_algorithms:
        try:
            writes = analyzer.analyze_linear_system_construction(app_name, algorithm)
            total_all_writes += writes
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")
    
    print(f"🎯 总结")
    print("=" * 80)
    print(f"4个定位算法总RRAM写入: {total_all_writes:,}")
    print(f"平均每算法: {total_all_writes//4:,}")
    print()
    print("关键发现:")
    print("1. EXP/LOG mapping确实需要，用于流形上的计算")
    print("2. Transpose操作大量使用，不能忽略")
    print("3. 线性系统求解本身也需要大量写入")
    print("4. 之前的分析遗漏了很多关键操作")


if __name__ == "__main__":
    main()
