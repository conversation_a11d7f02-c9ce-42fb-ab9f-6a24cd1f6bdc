#!/usr/bin/env python3
"""
详细分析线性方程系统的构建过程

重要问题：
1. 线性方程的左右都是什么？
2. 怎么计算来的，用了哪些写？
3. 公式是否正确？
4. EXP mapping和LOG mapping是否需要？
5. Transpose等操作是否遗漏？
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class LinearSystemDetailedAnalyzer:
    """详细分析线性方程系统构建过程"""
    
    def __init__(self):
        # 基于论文的统一位姿表示公式
        self.pose_representation_formulas = {
            'unified_pose': {
                'formula': 'ξ1 ⊕ ξ2 = -Log(R1 R2), t1 + R1 t2',
                'inverse': 'ξ1 ⊖ ξ2 = -Log(R2^T R1), R2^T(t1 - t2)',
                'exp_mapping': 'R = Exp(φ), where φ ∈ so(n)',
                'log_mapping': 'φ = Log(R), where R ∈ SO(n)',
                'operations_needed': [
                    'Exp(φ1) → R1',
                    'Exp(φ2) → R2', 
                    'R1 * R2 → matrix multiplication',
                    'R2^T → transpose',
                    'Log(R2^T R1) → log mapping',
                    'R2^T * (t1 - t2) → matrix-vector multiplication'
                ]
            }
        }
        
        # 线性方程系统 Ax = b 的详细构建过程
        self.linear_system_construction = {
            'gauss_newton': {
                'description': 'Gauss-Newton方法求解非线性最小二乘',
                'system': 'H * Δx = -g',
                'left_side': {
                    'H': 'Hessian近似矩阵',
                    'formula': 'H = J^T * Ω * J',
                    'components': {
                        'J': 'Jacobian矩阵 ∂e/∂x',
                        'J^T': 'Jacobian转置',
                        'Ω': '信息矩阵 (协方差逆)',
                        'operations': [
                            ('Compute_Jacobian', 'J = ∂e/∂x', 'per_factor'),
                            ('Transpose', 'J^T', 'per_factor'),
                            ('StaticVMM', 'J^T * Ω', 'per_factor'),
                            ('StaticVMM', '(J^T * Ω) * J', 'per_factor'),
                            ('Accumulate', 'H += J^T * Ω * J', 'per_factor')
                        ]
                    }
                },
                'right_side': {
                    'g': '梯度向量',
                    'formula': 'g = J^T * Ω * e',
                    'components': {
                        'e': '误差向量',
                        'operations': [
                            ('Compute_Error', 'e = f(x)', 'per_factor'),
                            ('StaticVMM', 'J^T * Ω', 'per_factor'),
                            ('StaticVMM', '(J^T * Ω) * e', 'per_factor'),
                            ('Accumulate', 'g += J^T * Ω * e', 'per_factor')
                        ]
                    }
                },
                'solution': {
                    'method': 'Cholesky分解或稀疏求解',
                    'operations': [
                        ('Cholesky', 'H = L * L^T', 'once_per_iteration'),
                        ('Forward_Sub', 'L * y = -g', 'once_per_iteration'),
                        ('Backward_Sub', 'L^T * Δx = y', 'once_per_iteration')
                    ]
                }
            }
        }
        
        # 具体因子的Jacobian计算（需要EXP/LOG mapping）
        self.factor_jacobian_details = {
            'pose_pose_factor': {
                'error_function': 'e = Log(R_ij^{-1} * R_i^{-1} * R_j)',
                'jacobian_computation': [
                    ('Exp', 'R_i = Exp(φ_i)', 'RRAM_write'),
                    ('Exp', 'R_j = Exp(φ_j)', 'RRAM_write'),
                    ('Transpose', 'R_i^T', 'RRAM_write'),
                    ('StaticVMM', 'R_i^T * R_j', 'RRAM_write'),
                    ('StaticVMM', 'R_ij^T * (R_i^T * R_j)', 'RRAM_write'),
                    ('Log', 'Log(result)', 'RRAM_write'),
                    ('Jacobian_Right', '∂Log(R)/∂φ', 'RRAM_write'),
                    ('Jacobian_Left', '∂(R_i^T * R_j)/∂φ_i', 'RRAM_write'),
                    ('Jacobian_Left', '∂(R_i^T * R_j)/∂φ_j', 'RRAM_write')
                ],
                'writes_per_factor': 'depends_on_manifold_dim'
            },
            
            'pose_landmark_factor': {
                'error_function': 'e = h(T_i * p_j) - z_ij',
                'jacobian_computation': [
                    ('Exp', 'T_i = Exp(ξ_i)', 'RRAM_write'),
                    ('Transform', 'T_i * p_j', 'RRAM_write'),
                    ('Project', 'h(T_i * p_j)', 'RRAM_write'),
                    ('VP', 'e = h(...) - z_ij', 'RRAM_write'),
                    ('Jacobian_SE3', '∂h/∂ξ_i', 'RRAM_write'),
                    ('Jacobian_Point', '∂h/∂p_j', 'RRAM_write')
                ],
                'writes_per_factor': 'depends_on_observation_dim'
            }
        }
        
        # 基于论文Table 2的完整算法信息
        self.complete_algorithm_info = {
            'robot_localization': {
                'variable_dim': 3,
                'factor_types': ['LiDAR', 'GPS'],
                'factor_count': 212,
                'mathematical_formulas': {
                    'LiDAR': {
                        'error_function': 'e = (xi ⊖ xj) ⊖ zij',
                        'detailed_formula': 'e = -Log(R_ij^T * R_i^T * R_j) + t_ij - R_i^T(t_j - t_i)',
                        'variables': ['xi ∈ SE(3)', 'xj ∈ SE(3)', 'zij ∈ se(3)'],
                        'operations': [
                            ('Store', 'xi (6DOF pose)', 6),
                            ('Store', 'xj (6DOF pose)', 6),
                            ('Store', 'zij (measurement)', 6),
                            ('Exp', 'R_i = Exp(φ_i)', 9),
                            ('Exp', 'R_j = Exp(φ_j)', 9),
                            ('Transpose', 'R_i^T', 9),
                            ('StaticVMM', 'R_i^T * R_j', 9),
                            ('Transpose', 'R_ij^T', 9),
                            ('StaticVMM', 'R_ij^T * (R_i^T * R_j)', 9),
                            ('Log', 'Log(result)', 3),
                            ('VP', 't_ij - R_i^T(t_j - t_i)', 6),
                            ('Jacobian_SE3', '∂e/∂xi', 18),
                            ('Jacobian_SE3', '∂e/∂xj', 18)
                        ]
                    },
                    'GPS': {
                        'error_function': 'e = h(x) - z',
                        'detailed_formula': 'e = position(x) - gps_measurement',
                        'variables': ['x ∈ SE(3)', 'z ∈ R³'],
                        'operations': [
                            ('Store', 'x (pose)', 6),
                            ('Store', 'z (GPS)', 3),
                            ('Extract', 'position(x)', 3),
                            ('VP', 'e = pos - z', 3),
                            ('Jacobian_Position', '∂pos/∂x', 18)
                        ]
                    }
                }
            },
            'robot_planning': {
                'variable_dim': 6,
                'factor_types': ['Smooth', 'Collision-free'],
                'factor_count': 52,
                'mathematical_formulas': {
                    'Smooth': {
                        'error_function': 'e = ||xi+1 - xi||²',
                        'detailed_formula': 'e = (xi+1 - xi)^T * Q * (xi+1 - xi)',
                        'variables': ['xi ∈ R⁶', 'xi+1 ∈ R⁶', 'Q ∈ R⁶ˣ⁶'],
                        'operations': [
                            ('Store', 'xi', 6),
                            ('Store', 'xi+1', 6),
                            ('VP', 'diff = xi+1 - xi', 6),
                            ('StaticVMM', 'Q * diff', 6),
                            ('VP', 'diff^T * (Q * diff)', 1),
                            ('Jacobian_Linear', '∂e/∂xi', 6),
                            ('Jacobian_Linear', '∂e/∂xi+1', 6)
                        ]
                    },
                    'Collision-free': {
                        'error_function': 'e = max(0, threshold - distance(x, obstacles))',
                        'detailed_formula': 'e = max(0, d_safe - min_i(||x - obs_i||))',
                        'variables': ['x ∈ R⁶', 'obstacles ∈ R^(n×3)'],
                        'operations': [
                            ('Store', 'x', 6),
                            ('Store', 'obstacles', 30),  # 假设10个障碍物
                            ('DynamicVMM', 'distances to all obstacles', 10),
                            ('Min', 'min distance', 1),
                            ('VP', 'e = d_safe - d_min', 1),
                            ('Jacobian_Distance', '∂d/∂x', 6)
                        ]
                    }
                }
            },
            'robot_control': {
                'variable_dim': (3, 2),  # (state_dim, control_dim)
                'factor_types': ['Dynamics'],
                'factor_count': 61,
                'mathematical_formulas': {
                    'Dynamics': {
                        'error_function': 'e = xi+1 - f(xi, ui)',
                        'detailed_formula': 'e = xi+1 - (A*xi + B*ui + c)',
                        'variables': ['xi ∈ R³', 'ui ∈ R²', 'xi+1 ∈ R³'],
                        'operations': [
                            ('Store', 'xi', 3),
                            ('Store', 'ui', 2),
                            ('Store', 'xi+1', 3),
                            ('StaticVMM', 'A * xi', 3),
                            ('StaticVMM', 'B * ui', 3),
                            ('VP', 'A*xi + B*ui + c', 3),
                            ('VP', 'e = xi+1 - f(xi,ui)', 3),
                            ('Jacobian_State', '∂f/∂xi', 9),
                            ('Jacobian_Control', '∂f/∂ui', 6)
                        ]
                    }
                }
            },
            'manipulator_localization': {
                'variable_dim': 2,
                'factor_types': ['Prior'],
                'factor_count': 1,
                'mathematical_formulas': {
                    'Prior': {
                        'error_function': 'e = x - x0',
                        'detailed_formula': 'e = x - prior_mean',
                        'variables': ['x ∈ R²', 'x0 ∈ R²'],
                        'operations': [
                            ('Store', 'x', 2),
                            ('Store', 'x0', 2),
                            ('VP', 'e = x - x0', 2),
                            ('Jacobian_Identity', '∂e/∂x = I', 4)
                        ]
                    }
                }
            },
            'manipulator_planning': {
                'variable_dim': 4,
                'factor_types': ['Smooth', 'Collision-free'],
                'factor_count': 59,
                'mathematical_formulas': {
                    'Smooth': {
                        'error_function': 'e = ||qi+1 - qi||²',
                        'detailed_formula': 'e = (qi+1 - qi)^T * Q * (qi+1 - qi)',
                        'variables': ['qi ∈ R⁴', 'qi+1 ∈ R⁴'],
                        'operations': [
                            ('Store', 'qi', 4),
                            ('Store', 'qi+1', 4),
                            ('VP', 'diff = qi+1 - qi', 4),
                            ('StaticVMM', 'Q * diff', 4),
                            ('VP', 'diff^T * (Q * diff)', 1),
                            ('Jacobian_Linear', '∂e/∂qi', 4),
                            ('Jacobian_Linear', '∂e/∂qi+1', 4)
                        ]
                    },
                    'Collision-free': {
                        'error_function': 'e = max(0, threshold - distance(q, obstacles))',
                        'detailed_formula': 'e = max(0, d_safe - min_i(||FK(q) - obs_i||))',
                        'variables': ['q ∈ R⁴', 'obstacles ∈ R^(n×3)'],
                        'operations': [
                            ('Store', 'q', 4),
                            ('Store', 'obstacles', 30),
                            ('ForwardKinematics', 'FK(q)', 12),  # 4个关节的位置
                            ('DynamicVMM', 'distances', 10),
                            ('Min', 'min distance', 1),
                            ('VP', 'e = d_safe - d_min', 1),
                            ('Jacobian_FK', '∂FK/∂q', 12),
                            ('Jacobian_Distance', '∂d/∂FK', 3)
                        ]
                    }
                }
            },
            'manipulator_control': {
                'variable_dim': (2, 2),
                'factor_types': ['Dynamics'],
                'factor_count': 61,
                'mathematical_formulas': {
                    'Dynamics': {
                        'error_function': 'e = qi+1 - f(qi, τi)',
                        'detailed_formula': 'e = qi+1 - (qi + dt * (M^(-1) * (τi - C*qi - G)))',
                        'variables': ['qi ∈ R²', 'τi ∈ R²', 'qi+1 ∈ R²'],
                        'operations': [
                            ('Store', 'qi', 2),
                            ('Store', 'τi', 2),
                            ('Store', 'qi+1', 2),
                            ('DynamicVMM', 'M(qi)', 4),  # 质量矩阵
                            ('DynamicVMM', 'C(qi)', 4),  # 科里奥利矩阵
                            ('DynamicVMM', 'G(qi)', 2),  # 重力向量
                            ('Inverse', 'M^(-1)', 4),
                            ('StaticVMM', 'M^(-1) * (τi - C*qi - G)', 2),
                            ('VP', 'qi+1_pred = qi + dt * qidot', 2),
                            ('VP', 'e = qi+1 - qi+1_pred', 2),
                            ('Jacobian_Dynamics', '∂f/∂qi', 4),
                            ('Jacobian_Dynamics', '∂f/∂τi', 4)
                        ]
                    }
                }
            },
            'autovehicle_localization': {
                'variable_dim': 3,
                'factor_types': ['LiDAR', 'GPS'],
                'factor_count': 251,
                'mathematical_formulas': {
                    'LiDAR': {
                        'error_function': 'e = (xi ⊖ xj) ⊖ zij',
                        'detailed_formula': 'e = -Log(R_ij^T * R_i^T * R_j) + t_ij - R_i^T(t_j - t_i)',
                        'variables': ['xi ∈ SE(3)', 'xj ∈ SE(3)', 'zij ∈ se(3)'],
                        'operations': [
                            ('Store', 'xi (vehicle pose)', 6),
                            ('Store', 'xj (landmark)', 6),
                            ('Store', 'zij (LiDAR scan)', 6),
                            ('Exp', 'R_i = Exp(φ_i)', 9),
                            ('Exp', 'R_j = Exp(φ_j)', 9),
                            ('Transpose', 'R_i^T', 9),
                            ('StaticVMM', 'R_i^T * R_j', 9),
                            ('Transpose', 'R_ij^T', 9),
                            ('StaticVMM', 'R_ij^T * (R_i^T * R_j)', 9),
                            ('Log', 'Log(result)', 3),
                            ('VP', 't_ij - R_i^T(t_j - t_i)', 6),
                            ('Jacobian_SE3', '∂e/∂xi', 18),
                            ('Jacobian_SE3', '∂e/∂xj', 18)
                        ]
                    },
                    'GPS': {
                        'error_function': 'e = h(x) - z',
                        'detailed_formula': 'e = position(x) - gps_measurement',
                        'variables': ['x ∈ SE(3)', 'z ∈ R³'],
                        'operations': [
                            ('Store', 'x (vehicle pose)', 6),
                            ('Store', 'z (GPS)', 3),
                            ('Extract', 'position(x)', 3),
                            ('VP', 'e = pos - z', 3),
                            ('Jacobian_Position', '∂pos/∂x', 18)
                        ]
                    }
                }
            },
            'autovehicle_planning': {
                'variable_dim': 6,
                'factor_types': ['Smooth', 'Collision-free', 'Kinematics'],
                'factor_count': 17,
                'mathematical_formulas': {
                    'Smooth': {
                        'error_function': 'e = ||xi+1 - xi||²',
                        'detailed_formula': 'e = (xi+1 - xi)^T * Q * (xi+1 - xi)',
                        'variables': ['xi ∈ R⁶', 'xi+1 ∈ R⁶'],
                        'operations': [
                            ('Store', 'xi (vehicle state)', 6),
                            ('Store', 'xi+1', 6),
                            ('VP', 'diff = xi+1 - xi', 6),
                            ('StaticVMM', 'Q * diff', 6),
                            ('VP', 'diff^T * (Q * diff)', 1),
                            ('Jacobian_Linear', '∂e/∂xi', 6),
                            ('Jacobian_Linear', '∂e/∂xi+1', 6)
                        ]
                    },
                    'Collision-free': {
                        'error_function': 'e = max(0, threshold - distance(x, obstacles))',
                        'detailed_formula': 'e = max(0, d_safe - min_i(||x - obs_i||))',
                        'variables': ['x ∈ R⁶', 'obstacles ∈ R^(n×3)'],
                        'operations': [
                            ('Store', 'x', 6),
                            ('Store', 'obstacles', 30),
                            ('DynamicVMM', 'distances', 10),
                            ('Min', 'min distance', 1),
                            ('VP', 'e = d_safe - d_min', 1),
                            ('Jacobian_Distance', '∂d/∂x', 6)
                        ]
                    },
                    'Kinematics': {
                        'error_function': 'e = xi+1 - f_kin(xi, ui)',
                        'detailed_formula': 'e = xi+1 - bicycle_model(xi, ui)',
                        'variables': ['xi ∈ R⁶', 'ui ∈ R²', 'xi+1 ∈ R⁶'],
                        'operations': [
                            ('Store', 'xi (x,y,θ,v,φ,a)', 6),
                            ('Store', 'ui (δ,a)', 2),
                            ('Store', 'xi+1', 6),
                            ('Trigonometric', 'sin(θ), cos(θ)', 2),
                            ('DynamicVMM', 'bicycle kinematics', 6),
                            ('VP', 'e = xi+1 - f_kin', 6),
                            ('Jacobian_Kinematics', '∂f_kin/∂xi', 36),
                            ('Jacobian_Kinematics', '∂f_kin/∂ui', 12)
                        ]
                    }
                }
            },
            'autovehicle_control': {
                'variable_dim': (5, 2),
                'factor_types': ['Kinematics', 'Dynamics'],
                'factor_count': 13,
                'mathematical_formulas': {
                    'Kinematics': {
                        'error_function': 'e = xi+1 - f_kin(xi, ui)',
                        'detailed_formula': 'e = xi+1 - bicycle_model(xi, ui)',
                        'variables': ['xi ∈ R⁵', 'ui ∈ R²', 'xi+1 ∈ R⁵'],
                        'operations': [
                            ('Store', 'xi (x,y,θ,v,φ)', 5),
                            ('Store', 'ui (δ,a)', 2),
                            ('Store', 'xi+1', 5),
                            ('Trigonometric', 'sin(θ), cos(θ)', 2),
                            ('DynamicVMM', 'bicycle kinematics', 5),
                            ('VP', 'e = xi+1 - f_kin', 5),
                            ('Jacobian_Kinematics', '∂f_kin/∂xi', 25),
                            ('Jacobian_Kinematics', '∂f_kin/∂ui', 10)
                        ]
                    },
                    'Dynamics': {
                        'error_function': 'e = xi+1 - f_dyn(xi, ui)',
                        'detailed_formula': 'e = xi+1 - vehicle_dynamics(xi, ui)',
                        'variables': ['xi ∈ R⁵', 'ui ∈ R²', 'xi+1 ∈ R⁵'],
                        'operations': [
                            ('Store', 'xi', 5),
                            ('Store', 'ui', 2),
                            ('Store', 'xi+1', 5),
                            ('DynamicVMM', 'tire forces', 8),
                            ('DynamicVMM', 'vehicle dynamics', 10),
                            ('VP', 'e = xi+1 - f_dyn', 5),
                            ('Jacobian_Dynamics', '∂f_dyn/∂xi', 25),
                            ('Jacobian_Dynamics', '∂f_dyn/∂ui', 10)
                        ]
                    }
                }
            },
            'quadrotor_localization': {
                'variable_dim': 6,
                'factor_types': ['IMU', 'Prior'],
                'factor_count': 21,
                'mathematical_formulas': {
                    'IMU': {
                        'error_function': 'e = [eω, ea, ep, ev, ebω, eba]',
                        'detailed_formula': 'e = IMU_preintegration_error(xi, xi+1, vi, bi)',
                        'variables': ['xi ∈ SE(3)', 'xi+1 ∈ SE(3)', 'vi ∈ R³', 'bi ∈ R⁶'],
                        'operations': [
                            ('Store', 'xi (pose)', 6),
                            ('Store', 'xi+1 (pose)', 6),
                            ('Store', 'vi (velocity)', 3),
                            ('Store', 'bi (bias)', 6),
                            ('Store', 'ωm (gyro)', 3),
                            ('Store', 'am (accel)', 3),
                            ('Exp', 'R_i = Exp(φ_i)', 9),
                            ('Exp', 'R_i+1 = Exp(φ_i+1)', 9),
                            ('Transpose', 'R_i^T', 9),
                            ('StaticVMM', 'R_i^T * R_i+1', 9),
                            ('Log', 'Log(R_i^T * R_i+1)', 3),
                            ('VP', 'rotation error', 3),
                            ('VP', 'translation error', 3),
                            ('VP', 'velocity error', 3),
                            ('VP', 'bias error', 6),
                            ('Jacobian_IMU', '∂e/∂xi', 36),
                            ('Jacobian_IMU', '∂e/∂xi+1', 36),
                            ('Jacobian_IMU', '∂e/∂vi', 18),
                            ('Jacobian_IMU', '∂e/∂bi', 36)
                        ]
                    },
                    'Prior': {
                        'error_function': 'e = x - x0',
                        'detailed_formula': 'e = pose - prior_pose',
                        'variables': ['x ∈ SE(3)', 'x0 ∈ SE(3)'],
                        'operations': [
                            ('Store', 'x (pose)', 6),
                            ('Store', 'x0 (prior)', 6),
                            ('Exp', 'R = Exp(φ)', 9),
                            ('Exp', 'R0 = Exp(φ0)', 9),
                            ('Transpose', 'R0^T', 9),
                            ('StaticVMM', 'R0^T * R', 9),
                            ('Log', 'Log(R0^T * R)', 3),
                            ('VP', 't - t0', 3),
                            ('Jacobian_SE3', '∂e/∂x', 36)
                        ]
                    }
                }
            },
            'quadrotor_planning': {
                'variable_dim': 12,
                'factor_types': ['Smooth', 'Collision-free', 'Kinematics'],
                'factor_count': 17,
                'mathematical_formulas': {
                    'Smooth': {
                        'error_function': 'e = ||xi+1 - xi||²',
                        'detailed_formula': 'e = (xi+1 - xi)^T * Q * (xi+1 - xi)',
                        'variables': ['xi ∈ R¹²', 'xi+1 ∈ R¹²'],
                        'operations': [
                            ('Store', 'xi (full state)', 12),
                            ('Store', 'xi+1', 12),
                            ('VP', 'diff = xi+1 - xi', 12),
                            ('StaticVMM', 'Q * diff', 12),
                            ('VP', 'diff^T * (Q * diff)', 1),
                            ('Jacobian_Linear', '∂e/∂xi', 12),
                            ('Jacobian_Linear', '∂e/∂xi+1', 12)
                        ]
                    },
                    'Collision-free': {
                        'error_function': 'e = max(0, threshold - distance(x, obstacles))',
                        'detailed_formula': 'e = max(0, d_safe - min_i(||pos(x) - obs_i||))',
                        'variables': ['x ∈ R¹²', 'obstacles ∈ R^(n×3)'],
                        'operations': [
                            ('Store', 'x', 12),
                            ('Store', 'obstacles', 30),
                            ('Extract', 'position from state', 3),
                            ('DynamicVMM', 'distances', 10),
                            ('Min', 'min distance', 1),
                            ('VP', 'e = d_safe - d_min', 1),
                            ('Jacobian_Position', '∂pos/∂x', 36),
                            ('Jacobian_Distance', '∂d/∂pos', 3)
                        ]
                    },
                    'Kinematics': {
                        'error_function': 'e = xi+1 - f_kin(xi, ui)',
                        'detailed_formula': 'e = xi+1 - quadrotor_kinematics(xi, ui)',
                        'variables': ['xi ∈ R¹²', 'ui ∈ R⁴', 'xi+1 ∈ R¹²'],
                        'operations': [
                            ('Store', 'xi (pos,vel,att,ω)', 12),
                            ('Store', 'ui (thrust,τ)', 4),
                            ('Store', 'xi+1', 12),
                            ('Exp', 'R = Exp(φ)', 9),
                            ('DynamicVMM', 'quadrotor kinematics', 12),
                            ('VP', 'e = xi+1 - f_kin', 12),
                            ('Jacobian_Kinematics', '∂f_kin/∂xi', 144),
                            ('Jacobian_Kinematics', '∂f_kin/∂ui', 48)
                        ]
                    }
                }
            },
            'quadrotor_control': {
                'variable_dim': (12, 5),
                'factor_types': ['Kinematics', 'Dynamics'],
                'factor_count': 13,
                'mathematical_formulas': {
                    'Kinematics': {
                        'error_function': 'e = xi+1 - f_kin(xi, ui)',
                        'detailed_formula': 'e = xi+1 - quadrotor_kinematics(xi, ui)',
                        'variables': ['xi ∈ R¹²', 'ui ∈ R⁵', 'xi+1 ∈ R¹²'],
                        'operations': [
                            ('Store', 'xi (pos,vel,att,ω)', 12),
                            ('Store', 'ui (f1,f2,f3,f4,τz)', 5),
                            ('Store', 'xi+1', 12),
                            ('Exp', 'R = Exp(φ)', 9),
                            ('DynamicVMM', 'thrust allocation', 12),
                            ('DynamicVMM', 'quadrotor kinematics', 12),
                            ('VP', 'e = xi+1 - f_kin', 12),
                            ('Jacobian_Kinematics', '∂f_kin/∂xi', 144),
                            ('Jacobian_Kinematics', '∂f_kin/∂ui', 60)
                        ]
                    },
                    'Dynamics': {
                        'error_function': 'e = xi+1 - f_dyn(xi, ui)',
                        'detailed_formula': 'e = xi+1 - quadrotor_dynamics(xi, ui)',
                        'variables': ['xi ∈ R¹²', 'ui ∈ R⁵', 'xi+1 ∈ R¹²'],
                        'operations': [
                            ('Store', 'xi', 12),
                            ('Store', 'ui', 5),
                            ('Store', 'xi+1', 12),
                            ('Exp', 'R = Exp(φ)', 9),
                            ('DynamicVMM', 'aerodynamic forces', 15),
                            ('DynamicVMM', 'quadrotor dynamics', 24),
                            ('VP', 'e = xi+1 - f_dyn', 12),
                            ('Jacobian_Dynamics', '∂f_dyn/∂xi', 144),
                            ('Jacobian_Dynamics', '∂f_dyn/∂ui', 60)
                        ]
                    }
                }
            }
        }
    
    def analyze_complete_algorithm(self, app_name: str, algorithm: str):
        """完整分析单个算法的公式和线性系统构建过程"""

        algorithm_key = f'{app_name}_{algorithm}'

        print(f"\n🔍 完整分析: {app_name} - {algorithm}")
        print("=" * 80)

        # 获取算法信息
        if algorithm_key not in self.complete_algorithm_info:
            print(f"❌ 未找到算法信息: {algorithm_key}")
            return 0

        algo_info = self.complete_algorithm_info[algorithm_key]

        # 显示算法基本信息
        self._display_algorithm_info(algo_info, algorithm_key)

        # 显示数学公式
        self._display_mathematical_formulas(algo_info)

        # 分析线性系统构建
        variable_dim = algo_info['variable_dim']
        if isinstance(variable_dim, tuple):
            variable_dim = variable_dim[0]  # 使用状态维度

        num_factors = algo_info['factor_count']

        self._analyze_hessian_construction_detailed(algo_info, variable_dim, num_factors)
        self._analyze_gradient_construction_detailed(algo_info, variable_dim, num_factors)
        self._analyze_system_solution(variable_dim)

        return self._calculate_total_writes_detailed(algo_info, variable_dim, num_factors)

    def _display_algorithm_info(self, algo_info: Dict, algorithm_key: str):
        """显示算法基本信息"""

        print(f"📊 算法信息:")
        variable_dim = algo_info['variable_dim']
        if isinstance(variable_dim, tuple):
            print(f"   变量维度: 状态{variable_dim[0]}维, 控制{variable_dim[1]}维")
        else:
            print(f"   变量维度: {variable_dim}")
        print(f"   因子类型: {algo_info['factor_types']}")
        print(f"   因子总数: {algo_info['factor_count']}")
        print()

    def _display_mathematical_formulas(self, algo_info: Dict):
        """显示数学公式"""

        print("📋 数学公式详解:")
        print("-" * 60)

        for factor_type, formula_info in algo_info['mathematical_formulas'].items():
            print(f"\n🔹 {factor_type} 因子:")
            print(f"   误差函数: {formula_info['error_function']}")
            print(f"   详细公式: {formula_info['detailed_formula']}")
            print(f"   变量定义: {', '.join(formula_info['variables'])}")
            print()

            print("   具体操作过程:")
            total_writes = 0
            for op_type, description, writes in formula_info['operations']:
                if isinstance(writes, int):
                    total_writes += writes
                    print(f"      {op_type}: {description} - {writes} 次RRAM写入")
                else:
                    print(f"      {op_type}: {description} - SIMD计算")

            print(f"   该因子类型每次写入: {total_writes}")
            print()
        print()
    
    def _analyze_hessian_construction(self, variable_dim: int, num_factors: int):
        """分析Hessian矩阵 H = J^T * Ω * J 的构建"""
        
        print("📋 Hessian矩阵构建: H = J^T * Ω * J")
        print("-" * 60)
        
        # 假设每个因子的误差维度
        if variable_dim <= 3:
            error_dim = 2  # 2D观测
        else:
            error_dim = 3  # 3D观测
        
        print(f"每个因子的操作:")
        print(f"1️⃣ 计算Jacobian: J ({error_dim}×{variable_dim})")
        
        # Jacobian计算需要的操作
        jacobian_ops = self._get_jacobian_operations(variable_dim, error_dim)
        jacobian_writes = sum(op[2] for op in jacobian_ops if op[2] != 'SIMD')
        
        for op_type, description, writes in jacobian_ops:
            status = "RRAM写入" if writes != 'SIMD' else "SIMD计算"
            print(f"   {op_type}: {description} - {writes} ({status})")
        
        print(f"   Jacobian计算总写入: {jacobian_writes}")
        print()
        
        print(f"2️⃣ 计算J^T:")
        transpose_writes = error_dim * variable_dim
        print(f"   Transpose: J^T ({variable_dim}×{error_dim}) - {transpose_writes} 写入")
        print()
        
        print(f"3️⃣ 计算J^T * Ω:")
        jt_omega_writes = variable_dim * error_dim
        print(f"   StaticVMM: J^T * Ω - {jt_omega_writes} 写入")
        print()
        
        print(f"4️⃣ 计算(J^T * Ω) * J:")
        jt_omega_j_writes = variable_dim * variable_dim
        print(f"   StaticVMM: (J^T * Ω) * J - {jt_omega_j_writes} 写入")
        print()
        
        print(f"5️⃣ 累加到Hessian:")
        accumulate_writes = variable_dim * variable_dim
        print(f"   Accumulate: H += result - {accumulate_writes} 写入")
        print()
        
        hessian_writes_per_factor = (jacobian_writes + transpose_writes + 
                                   jt_omega_writes + jt_omega_j_writes + accumulate_writes)
        total_hessian_writes = hessian_writes_per_factor * num_factors
        
        print(f"🎯 Hessian构建总结:")
        print(f"   每因子写入: {hessian_writes_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   Hessian总写入: {total_hessian_writes:,}")
        print()
    
    def _analyze_gradient_construction(self, variable_dim: int, num_factors: int):
        """分析梯度向量 g = J^T * Ω * e 的构建"""
        
        print("📋 梯度向量构建: g = J^T * Ω * e")
        print("-" * 60)
        
        if variable_dim <= 3:
            error_dim = 2
        else:
            error_dim = 3
        
        print(f"每个因子的操作:")
        print(f"1️⃣ 计算误差: e ({error_dim}维)")
        
        # 误差计算需要的操作（包括EXP/LOG mapping）
        error_ops = self._get_error_operations(variable_dim, error_dim)
        error_writes = sum(op[2] for op in error_ops if op[2] != 'SIMD')
        
        for op_type, description, writes in error_ops:
            status = "RRAM写入" if writes != 'SIMD' else "SIMD计算"
            print(f"   {op_type}: {description} - {writes} ({status})")
        
        print(f"   误差计算总写入: {error_writes}")
        print()
        
        print(f"2️⃣ 计算J^T * Ω * e:")
        jt_omega_e_writes = variable_dim
        print(f"   StaticVMM: J^T * Ω * e - {jt_omega_e_writes} 写入")
        print()
        
        print(f"3️⃣ 累加到梯度:")
        accumulate_writes = variable_dim
        print(f"   Accumulate: g += result - {accumulate_writes} 写入")
        print()
        
        gradient_writes_per_factor = error_writes + jt_omega_e_writes + accumulate_writes
        total_gradient_writes = gradient_writes_per_factor * num_factors
        
        print(f"🎯 梯度构建总结:")
        print(f"   每因子写入: {gradient_writes_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   梯度总写入: {total_gradient_writes:,}")
        print()
    
    def _analyze_system_solution(self, variable_dim: int):
        """分析线性系统求解 H * Δx = -g"""
        
        print("📋 线性系统求解: H * Δx = -g")
        print("-" * 60)
        
        print("1️⃣ Cholesky分解: H = L * L^T")
        cholesky_writes = (variable_dim * variable_dim * variable_dim) // 6  # 约1/6 n³
        print(f"   Cholesky分解写入: {cholesky_writes}")
        print()
        
        print("2️⃣ 前向替换: L * y = -g")
        forward_writes = variable_dim * variable_dim // 2
        print(f"   前向替换写入: {forward_writes}")
        print()
        
        print("3️⃣ 后向替换: L^T * Δx = y")
        backward_writes = variable_dim * variable_dim // 2
        print(f"   后向替换写入: {backward_writes}")
        print()
        
        total_solution_writes = cholesky_writes + forward_writes + backward_writes
        print(f"🎯 系统求解总写入: {total_solution_writes}")
        print()
    
    def _analyze_missing_operations(self, variable_dim: int, num_factors: int):
        """分析之前遗漏的关键操作"""
        
        print("📋 之前遗漏的关键操作")
        print("-" * 60)
        
        print("🔍 EXP/LOG Mapping操作:")
        exp_operations = num_factors * 2  # 每因子至少2个位姿
        log_operations = num_factors * 1  # 每因子1个误差计算
        print(f"   Exp mapping: {exp_operations} 次 (R = Exp(φ))")
        print(f"   Log mapping: {log_operations} 次 (φ = Log(R))")
        print()
        
        print("🔍 Transpose操作:")
        transpose_operations = num_factors * 3  # Jacobian转置 + 旋转矩阵转置
        print(f"   矩阵转置: {transpose_operations} 次")
        print()
        
        print("🔍 流形上的Jacobian计算:")
        manifold_jacobian = num_factors * variable_dim * 2  # 左右Jacobian
        print(f"   流形Jacobian: {manifold_jacobian} 次写入")
        print()
        
        total_missing = (exp_operations * 9 + log_operations * 3 + 
                        transpose_operations * variable_dim * variable_dim + 
                        manifold_jacobian)
        
        print(f"🎯 遗漏操作总写入: {total_missing:,}")
        print()
    
    def _get_jacobian_operations(self, variable_dim: int, error_dim: int):
        """获取Jacobian计算的具体操作"""
        
        return [
            ('Exp', f'R = Exp(φ) for poses', 9),
            ('Transform', 'Apply transformation', variable_dim),
            ('Project', 'Observation model h(x)', error_dim),
            ('Jacobian_SE3', '∂h/∂ξ (SE3 Jacobian)', error_dim * 6),
            ('Jacobian_Point', '∂h/∂p (Point Jacobian)', error_dim * 3),
            ('Chain_Rule', 'Chain rule application', 'SIMD')
        ]
    
    def _get_error_operations(self, variable_dim: int, error_dim: int):
        """获取误差计算的具体操作"""
        
        return [
            ('Exp', 'R_i = Exp(φ_i)', 9),
            ('Exp', 'R_j = Exp(φ_j)', 9),
            ('Transpose', 'R_i^T', 9),
            ('StaticVMM', 'R_i^T * R_j', 9),
            ('Log', 'Log(R_i^T * R_j)', 3),
            ('VP', 'Translation error', 3),
            ('Combine', 'Combine rotation and translation', error_dim)
        ]
    
    def _analyze_hessian_construction_detailed(self, algo_info: Dict, variable_dim: int, num_factors: int):
        """详细分析Hessian矩阵构建"""

        print("📋 Hessian矩阵构建: H = J^T * Ω * J")
        print("-" * 60)

        # 计算每个因子的Jacobian写入
        total_jacobian_writes = 0
        for factor_type, formula_info in algo_info['mathematical_formulas'].items():
            jacobian_writes = sum(writes for op_type, _, writes in formula_info['operations']
                                if isinstance(writes, int) and 'Jacobian' in op_type)
            total_jacobian_writes += jacobian_writes

        avg_jacobian_writes = total_jacobian_writes // len(algo_info['mathematical_formulas'])

        print(f"每个因子的Hessian构建:")
        print(f"1️⃣ 计算Jacobian: 平均 {avg_jacobian_writes} 次写入")
        print(f"2️⃣ 转置J^T: {variable_dim * 3} 次写入")  # 假设误差维度为3
        print(f"3️⃣ 计算J^T * Ω: {variable_dim * 3} 次写入")
        print(f"4️⃣ 计算(J^T * Ω) * J: {variable_dim * variable_dim} 次写入")
        print(f"5️⃣ 累加到H: {variable_dim * variable_dim} 次写入")

        hessian_per_factor = avg_jacobian_writes + variable_dim * 3 * 2 + variable_dim * variable_dim * 2
        total_hessian = hessian_per_factor * num_factors

        print(f"\n🎯 Hessian构建总结:")
        print(f"   每因子写入: {hessian_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   Hessian总写入: {total_hessian:,}")
        print()

    def _analyze_gradient_construction_detailed(self, algo_info: Dict, variable_dim: int, num_factors: int):
        """详细分析梯度向量构建"""

        print("📋 梯度向量构建: g = J^T * Ω * e")
        print("-" * 60)

        # 计算每个因子的误差计算写入
        total_error_writes = 0
        for factor_type, formula_info in algo_info['mathematical_formulas'].items():
            error_writes = sum(writes for op_type, _, writes in formula_info['operations']
                             if isinstance(writes, int) and 'Jacobian' not in op_type)
            total_error_writes += error_writes

        avg_error_writes = total_error_writes // len(algo_info['mathematical_formulas'])

        print(f"每个因子的梯度构建:")
        print(f"1️⃣ 计算误差e: 平均 {avg_error_writes} 次写入")
        print(f"2️⃣ 计算J^T * Ω * e: {variable_dim} 次写入")
        print(f"3️⃣ 累加到g: {variable_dim} 次写入")

        gradient_per_factor = avg_error_writes + variable_dim * 2
        total_gradient = gradient_per_factor * num_factors

        print(f"\n🎯 梯度构建总结:")
        print(f"   每因子写入: {gradient_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   梯度总写入: {total_gradient:,}")
        print()

    def _calculate_total_writes_detailed(self, algo_info: Dict, variable_dim: int, num_factors: int):
        """基于详细公式计算总RRAM写入次数"""

        # 因子操作写入
        total_factor_writes = 0
        for factor_type, formula_info in algo_info['mathematical_formulas'].items():
            factor_writes = sum(writes for op_type, _, writes in formula_info['operations']
                              if isinstance(writes, int))
            total_factor_writes += factor_writes * num_factors

        # Hessian构建额外写入
        hessian_extra = num_factors * (variable_dim * 3 * 2 + variable_dim * variable_dim * 2)

        # 梯度构建额外写入
        gradient_extra = num_factors * variable_dim * 2

        # 系统求解
        solution_writes = (variable_dim ** 3) // 6 + variable_dim ** 2

        total_writes = total_factor_writes + hessian_extra + gradient_extra + solution_writes

        print(f"🎯 总RRAM写入统计:")
        print(f"   因子操作: {total_factor_writes:,}")
        print(f"   Hessian额外: {hessian_extra:,}")
        print(f"   梯度额外: {gradient_extra:,}")
        print(f"   系统求解: {solution_writes:,}")
        print(f"   总计: {total_writes:,}")
        print()

        return total_writes
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()


def main():
    """主函数 - 完整分析所有12个算法的公式和过程"""

    analyzer = LinearSystemDetailedAnalyzer()

    print("🚀 完整分析所有12个算法的公式和线性系统构建过程")
    print("=" * 80)
    print("包含:")
    print("1. 每个算法的具体数学公式")
    print("2. 线性方程 H * Δx = -g 的完整构建过程")
    print("3. 所有RRAM写入操作的详细统计")
    print("4. EXP/LOG mapping和Transpose的具体使用")
    print()

    # 所有12个算法
    all_algorithms = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('manipulator', 'localization'),
        ('manipulator', 'planning'),
        ('manipulator', 'control'),
        ('autovehicle', 'localization'),
        ('autovehicle', 'planning'),
        ('autovehicle', 'control'),
        ('quadrotor', 'localization'),
        ('quadrotor', 'planning'),
        ('quadrotor', 'control'),
    ]

    total_all_writes = 0

    for app_name, algorithm in all_algorithms:
        try:
            writes = analyzer.analyze_complete_algorithm(app_name, algorithm)
            total_all_writes += writes
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")

    print(f"🎯 总结")
    print("=" * 80)
    print(f"12个算法总RRAM写入: {total_all_writes:,}")
    print(f"平均每算法: {total_all_writes//12:,}")
    print()
    print("关键发现:")
    print("1. 每个算法都有特定的数学公式和因子类型")
    print("2. EXP/LOG mapping在所有位姿相关算法中都必需")
    print("3. Transpose操作在矩阵计算中大量使用")
    print("4. 线性系统求解的复杂度随变量维度立方增长")
    print("5. 控制算法比定位算法需要更多的动力学计算")


if __name__ == "__main__":
    main()
