#!/usr/bin/env python3
"""
RRAM Write Analysis Report Generator

Generates detailed analysis report for RRAM write operations in 12 factor graph algorithms.
Based on realistic problem scales and Orianna compiler architecture.
"""

import matplotlib.pyplot as plt
import numpy as np
from realistic_rram_write_analyzer import Realistic<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RRAMWriteAnalysis
from typing import List
import json

class RRAMReportGenerator:
    """Generate comprehensive RRAM write analysis reports"""
    
    def __init__(self):
        self.analyzer = RealisticRRAMAnalyzer()
    
    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report"""
        
        print("🚀 Generating Comprehensive RRAM Write Analysis Report")
        print("=" * 80)
        
        # Run analysis
        results = self.analyzer.analyze_all_algorithms()
        
        # Generate different views of the data
        self.print_detailed_breakdown(results)
        self.print_application_comparison(results)
        self.print_algorithm_type_analysis(results)
        self.print_scalability_analysis(results)
        self.save_results_json(results)
        
        return results
    
    def print_detailed_breakdown(self, results: List[RRAMWriteAnalysis]):
        """Print detailed breakdown of RRAM writes by operation type"""
        
        print(f"\n{'='*100}")
        print("📊 DETAILED RRAM WRITE BREAKDOWN BY OPERATION TYPE")
        print(f"{'='*100}")
        
        print(f"{'Algorithm':<25} {'Factor':<10} {'Matrix':<10} {'QR':<12} {'Solve':<10} {'Total/Iter':<12}")
        print("-" * 100)
        
        for result in results:
            app_full = f"{result.app_name}_{result.algorithm}"
            print(f"{app_full:<25} {result.factor_computation_writes:<10,} "
                  f"{result.matrix_construction_writes:<10,} {result.qr_decomposition_writes:<12,} "
                  f"{result.back_substitution_writes:<10,} {result.total_writes_per_iteration:<12,}")
    
    def print_application_comparison(self, results: List[RRAMWriteAnalysis]):
        """Compare RRAM writes across different applications"""
        
        print(f"\n{'='*80}")
        print("🔍 COMPARISON BY APPLICATION TYPE")
        print(f"{'='*80}")
        
        # Group by application
        app_groups = {}
        for result in results:
            if result.app_name not in app_groups:
                app_groups[result.app_name] = []
            app_groups[result.app_name].append(result)
        
        for app_name, app_results in app_groups.items():
            total_writes = sum(r.total_writes_complete_solve for r in app_results)
            avg_writes = total_writes // len(app_results)
            
            print(f"\n{app_name.upper()}:")
            print(f"  Total writes: {total_writes:,}")
            print(f"  Average per algorithm: {avg_writes:,}")
            
            for result in app_results:
                print(f"    {result.algorithm}: {result.total_writes_complete_solve:,} writes "
                      f"({result.estimated_iterations} iterations)")
    
    def print_algorithm_type_analysis(self, results: List[RRAMWriteAnalysis]):
        """Analyze RRAM writes by algorithm type (localization, planning, control)"""
        
        print(f"\n{'='*80}")
        print("🎯 ANALYSIS BY ALGORITHM TYPE")
        print(f"{'='*80}")
        
        # Group by algorithm type
        algo_groups = {}
        for result in results:
            if result.algorithm not in algo_groups:
                algo_groups[result.algorithm] = []
            algo_groups[result.algorithm].append(result)
        
        for algo_type, algo_results in algo_groups.items():
            total_writes = sum(r.total_writes_complete_solve for r in algo_results)
            avg_writes = total_writes // len(algo_results)
            avg_iterations = sum(r.estimated_iterations for r in algo_results) // len(algo_results)
            
            print(f"\n{algo_type.upper()} ALGORITHMS:")
            print(f"  Total writes: {total_writes:,}")
            print(f"  Average per application: {avg_writes:,}")
            print(f"  Average iterations: {avg_iterations}")
            
            # Sort by write count
            sorted_results = sorted(algo_results, key=lambda x: x.total_writes_complete_solve, reverse=True)
            for result in sorted_results:
                matrix_size = f"{result.matrix_dimensions[0]}x{result.matrix_dimensions[1]}"
                print(f"    {result.app_name}: {result.total_writes_complete_solve:,} writes ({matrix_size})")
    
    def print_scalability_analysis(self, results: List[RRAMWriteAnalysis]):
        """Analyze how RRAM writes scale with problem size"""
        
        print(f"\n{'='*80}")
        print("📈 SCALABILITY ANALYSIS")
        print(f"{'='*80}")
        
        # Sort by matrix size (total elements)
        sorted_results = sorted(results, key=lambda x: x.matrix_dimensions[0] * x.matrix_dimensions[1])
        
        print(f"{'Algorithm':<25} {'Matrix Elements':<15} {'Writes/Element':<15} {'Efficiency':<12}")
        print("-" * 80)
        
        for result in sorted_results:
            matrix_elements = result.matrix_dimensions[0] * result.matrix_dimensions[1]
            writes_per_element = result.total_writes_per_iteration / max(matrix_elements, 1)
            
            # Efficiency metric: lower is better
            efficiency = "High" if writes_per_element < 100 else "Medium" if writes_per_element < 500 else "Low"
            
            app_full = f"{result.app_name}_{result.algorithm}"
            print(f"{app_full:<25} {matrix_elements:<15,} {writes_per_element:<15.1f} {efficiency:<12}")
    
    def print_hardware_implications(self, results: List[RRAMWriteAnalysis]):
        """Analyze hardware implications of RRAM write counts"""
        
        print(f"\n{'='*80}")
        print("⚡ HARDWARE IMPLICATIONS")
        print(f"{'='*80}")
        
        # Assume typical RRAM parameters
        write_energy_per_cell = 1e-12  # 1 pJ per write (typical)
        write_latency_per_cell = 10e-9  # 10 ns per write
        rram_endurance = 1e6  # 1M write cycles typical
        
        total_writes = sum(r.total_writes_complete_solve for r in results)
        
        print(f"Total RRAM writes across all algorithms: {total_writes:,}")
        print(f"Estimated energy consumption: {total_writes * write_energy_per_cell * 1e9:.2f} nJ")
        print(f"Estimated write latency: {total_writes * write_latency_per_cell * 1e3:.2f} ms")
        print(f"RRAM lifetime (at 1M endurance): {rram_endurance / (total_writes / len(results)):.0f} algorithm runs")
        
        # Find most write-intensive algorithms
        top_3 = sorted(results, key=lambda x: x.total_writes_complete_solve, reverse=True)[:3]
        print(f"\nMost RRAM-intensive algorithms:")
        for i, result in enumerate(top_3, 1):
            print(f"  {i}. {result.app_name}_{result.algorithm}: {result.total_writes_complete_solve:,} writes")
    
    def save_results_json(self, results: List[RRAMWriteAnalysis]):
        """Save results to JSON for further analysis"""
        
        data = []
        for result in results:
            data.append({
                'app_name': result.app_name,
                'algorithm': result.algorithm,
                'total_variables': result.total_variables,
                'total_factors': result.total_factors,
                'matrix_dimensions': result.matrix_dimensions,
                'factor_computation_writes': result.factor_computation_writes,
                'matrix_construction_writes': result.matrix_construction_writes,
                'qr_decomposition_writes': result.qr_decomposition_writes,
                'back_substitution_writes': result.back_substitution_writes,
                'total_writes_per_iteration': result.total_writes_per_iteration,
                'estimated_iterations': result.estimated_iterations,
                'total_writes_complete_solve': result.total_writes_complete_solve
            })
        
        with open('rram_write_analysis_results.json', 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"\n💾 Results saved to rram_write_analysis_results.json")
    
    def create_visualization(self, results: List[RRAMWriteAnalysis]):
        """Create visualization of RRAM write analysis"""
        
        try:
            import matplotlib.pyplot as plt
            
            # Prepare data
            apps = [f"{r.app_name}_{r.algorithm}" for r in results]
            total_writes = [r.total_writes_complete_solve for r in results]
            
            # Create bar chart
            plt.figure(figsize=(15, 8))
            bars = plt.bar(range(len(apps)), total_writes, color='steelblue', alpha=0.7)
            
            plt.xlabel('Algorithm')
            plt.ylabel('Total RRAM Writes')
            plt.title('RRAM Write Count Analysis - 12 Factor Graph Algorithms')
            plt.xticks(range(len(apps)), apps, rotation=45, ha='right')
            plt.yscale('log')  # Log scale due to large range
            
            # Add value labels on bars
            for bar, value in zip(bars, total_writes):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height(), 
                        f'{value:,}', ha='center', va='bottom', fontsize=8)
            
            plt.tight_layout()
            plt.savefig('rram_write_analysis.png', dpi=300, bbox_inches='tight')
            print(f"📊 Visualization saved to rram_write_analysis.png")
            
        except ImportError:
            print("📊 Matplotlib not available, skipping visualization")


def main():
    """Main execution"""
    
    generator = RRAMReportGenerator()
    results = generator.generate_comprehensive_report()
    generator.print_hardware_implications(results)
    generator.create_visualization(results)
    
    print(f"\n🎉 Comprehensive RRAM analysis complete!")
    print(f"📋 {len(results)} algorithms analyzed")
    print(f"💾 Results saved for further analysis")
    
    return results


if __name__ == "__main__":
    results = main()
