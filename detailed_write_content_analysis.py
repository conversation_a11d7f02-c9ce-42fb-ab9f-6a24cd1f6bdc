#!/usr/bin/env python3
"""
详细分析RRAM写入的具体内容

基于您的重要指正：
1. 仔细说明都在写什么内容
2. 误差公式应该是复杂的SLAM公式，不是简单的e=h(x)-z
3. 机械臂1维是错误的，应该是多自由度
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class DetailedWriteContentAnalyzer:
    """详细分析RRAM写入的具体内容"""
    
    def __init__(self):
        # 基于您提供图片的实际SLAM误差公式
        self.slam_error_formulas = {
            # 旋转误差 (基于您的图片)
            'rotation_error': {
                'formula': 'er(xi, xj) = (xi ⊖ xj) ⊖ zij',
                'detailed': 'er = -Log(Ri Rj)',
                'components': [
                    'ξi ⊕ ξj = -Log(Ri Rj)',
                    'Ri = Exp(φi)',
                    'Rj = Exp(φj)'
                ],
                'variables': ['φi', 'φj', 'Ri', 'Rj', 'zij'],
                'dimensions': {'φi': 3, 'φj': 3, 'Ri': 9, 'Rj': 9, 'zij': 3}
            },
            
            # 平移误差 (基于您的图片)
            'translation_error': {
                'formula': 'ep(xi, xj) = Ri^T(ti - tj)',
                'detailed': 'ep = ΔRij^T[Ri^T(ti - tj) - Δtij]',
                'components': [
                    'Ri^T(ti - tj)',
                    'ΔRij^T * result - Δtij'
                ],
                'variables': ['ti', 'tj', 'Ri', 'ΔRij', 'Δtij'],
                'dimensions': {'ti': 3, 'tj': 3, 'Ri': 9, 'ΔRij': 9, 'Δtij': 3}
            }
        }
        
        # 修正后的算法变量维度 (机械臂不应该是1维)
        self.corrected_variable_dimensions = {
            'robot_localization': {
                'poses': {'count': 25, 'dim_per_pose': 3},  # 2D位姿 (x,y,θ)
                'landmarks': {'count': 20, 'dim_per_landmark': 2},  # 2D路标
                'total_dim': 25*3 + 20*2  # 115维
            },
            'manipulator_localization': {
                'poses': {'count': 10, 'dim_per_pose': 6},  # 6DOF位姿
                'joint_states': {'count': 6, 'dim_per_joint': 1},  # 6个关节角度
                'total_dim': 10*6 + 6*1  # 66维，不是1维！
            },
            'autovehicle_localization': {
                'poses': {'count': 15, 'dim_per_pose': 6},  # 6DOF位姿
                'landmarks': {'count': 30, 'dim_per_landmark': 3},  # 3D路标
                'calibration': {'count': 3, 'dim_per_sensor': 6},  # 传感器标定
                'total_dim': 15*6 + 30*3 + 3*6  # 198维
            },
            'quadrotor_localization': {
                'poses': {'count': 8, 'dim_per_pose': 6},  # 6DOF位姿
                'velocities': {'count': 8, 'dim_per_velocity': 3},  # 3D速度
                'biases': {'count': 8, 'dim_per_bias': 6},  # IMU偏置
                'landmarks': {'count': 10, 'dim_per_landmark': 3},  # 3D特征点
                'total_dim': 8*6 + 8*3 + 8*6 + 10*3  # 132维
            }
        }
    
    def analyze_detailed_write_content(self, app_name: str, algorithm: str):
        """详细分析具体写入内容"""
        
        print(f"\n🔍 详细分析 {app_name} - {algorithm} 的具体写入内容")
        print("=" * 80)
        
        # 获取因子图结构
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        print(f"📊 因子图结构:")
        print(f"   变量节点: {len(nodes)} 类型")
        print(f"   因子数量: {len(factors)}")
        print()
        
        # 分析具体的SLAM误差公式写入
        if algorithm == 'localization':
            self._analyze_slam_localization_writes(app_name, factors)
        elif algorithm == 'planning':
            self._analyze_planning_writes(app_name, factors)
        elif algorithm == 'control':
            self._analyze_control_writes(app_name, factors)
    
    def _analyze_slam_localization_writes(self, app_name: str, factors: Dict):
        """分析SLAM定位的具体写入内容 (基于您的图片公式)"""
        
        print("📋 SLAM定位算法的具体写入内容 (基于复杂误差公式)")
        print("-" * 60)
        
        # 获取修正后的变量维度
        var_info = self.corrected_variable_dimensions.get(f'{app_name}_localization', {})
        total_dim = var_info.get('total_dim', 0)
        
        print(f"🔢 变量维度分析 (修正后):")
        for var_type, info in var_info.items():
            if var_type != 'total_dim':
                print(f"   {var_type}: {info}")
        print(f"   总变量维度: {total_dim}")
        print()
        
        print("📝 基于您图片的SLAM误差公式的具体写入:")
        print()
        
        # 1. 旋转误差计算的写入
        print("1️⃣ 旋转误差计算写入:")
        rotation_formula = self.slam_error_formulas['rotation_error']
        print(f"   公式: {rotation_formula['formula']}")
        print(f"   详细: {rotation_formula['detailed']}")
        print("   具体写入内容:")
        
        rotation_writes = 0
        for var, dim in rotation_formula['dimensions'].items():
            print(f"      {var}: {dim}个浮点数")
            rotation_writes += dim
        
        print(f"   每个旋转因子写入: {rotation_writes} 个浮点数")
        print()
        
        # 2. 平移误差计算的写入
        print("2️⃣ 平移误差计算写入:")
        translation_formula = self.slam_error_formulas['translation_error']
        print(f"   公式: {rotation_formula['formula']}")
        print(f"   详细: {translation_formula['detailed']}")
        print("   具体写入内容:")
        
        translation_writes = 0
        for var, dim in translation_formula['dimensions'].items():
            print(f"      {var}: {dim}个浮点数")
            translation_writes += dim
        
        print(f"   每个平移因子写入: {translation_writes} 个浮点数")
        print()
        
        # 3. Jacobian矩阵计算的写入
        print("3️⃣ Jacobian矩阵计算写入:")
        print("   基于复杂的SLAM Jacobian:")
        print("   ∂er/∂φi, ∂er/∂φj, ∂ep/∂ti, ∂ep/∂tj, ∂ep/∂φi, ∂ep/∂φj")
        
        # 估算Jacobian维度
        error_dim = 6  # 旋转误差3维 + 平移误差3维
        jacobian_elements = error_dim * total_dim
        print(f"   Jacobian矩阵: {error_dim}×{total_dim} = {jacobian_elements} 个元素")
        print(f"   每个因子的Jacobian写入: {jacobian_elements} 个浮点数")
        print()
        
        # 4. 线性系统构建的写入
        print("4️⃣ 线性系统构建写入:")
        print("   H = J^T * Ω * J (Hessian矩阵)")
        print("   b = J^T * Ω * e (梯度向量)")
        
        hessian_elements = total_dim * total_dim
        gradient_elements = total_dim
        
        print(f"   Hessian矩阵: {total_dim}×{total_dim} = {hessian_elements} 个元素")
        print(f"   梯度向量: {total_dim} 个元素")
        print(f"   线性系统总写入: {hessian_elements + gradient_elements} 个浮点数")
        print()
        
        # 总计算
        total_writes_per_factor = rotation_writes + translation_writes + jacobian_elements
        total_factors = len(factors)
        linear_system_writes = hessian_elements + gradient_elements
        
        total_writes = (total_writes_per_factor * total_factors) + linear_system_writes
        
        print(f"🎯 {app_name} 定位算法总写入:")
        print(f"   每因子写入: {total_writes_per_factor} 个浮点数")
        print(f"   因子数量: {total_factors}")
        print(f"   因子相关写入: {total_writes_per_factor * total_factors:,}")
        print(f"   线性系统写入: {linear_system_writes:,}")
        print(f"   总写入: {total_writes:,} 个浮点数")
        print()
    
    def _analyze_planning_writes(self, app_name: str, factors: Dict):
        """分析规划算法的写入内容"""
        
        print("📋 规划算法的具体写入内容")
        print("-" * 60)
        
        print("🔢 规划算法变量:")
        if app_name == 'robot':
            print("   路径点: 30个, 每个3维 (x,y,θ) = 90维")
            total_dim = 90
        elif app_name == 'manipulator':
            print("   关节配置: 25个, 每个6维 (6DOF) = 150维")
            total_dim = 150
        elif app_name == 'autovehicle':
            print("   状态点: 8个, 每个4维 (x,y,v,θ) = 32维")
            total_dim = 32
        elif app_name == 'quadrotor':
            print("   状态点: 8个, 每个6维 (位置+姿态) = 48维")
            total_dim = 48
        
        print(f"   总变量维度: {total_dim}")
        print()
        
        # 规划算法的约束写入
        print("📝 规划约束的具体写入:")
        print("1️⃣ 平滑性约束: ||si+1 - si||²")
        print("2️⃣ 避障约束: distance(si, obstacles) > threshold")
        print("3️⃣ 边界约束: s0 = start, sN = goal")
        
        constraint_writes = total_dim * 3  # 三种约束类型
        total_factors = len(factors)
        
        print(f"   每因子约束写入: {constraint_writes} 个浮点数")
        print(f"   总约束写入: {constraint_writes * total_factors:,}")
        print()
    
    def _analyze_control_writes(self, app_name: str, factors: Dict):
        """分析控制算法的写入内容"""
        
        print("📋 控制算法的具体写入内容")
        print("-" * 60)
        
        print("🔢 控制算法变量:")
        if app_name == 'robot':
            print("   状态: 20个时间步, 每个3维 (x,y,θ) = 60维")
            print("   控制: 19个时间步, 每个2维 (v,ω) = 38维")
            total_dim = 98
        elif app_name == 'manipulator':
            print("   关节状态: 20个时间步, 每个6维 = 120维")
            print("   关节力矩: 19个时间步, 每个6维 = 114维")
            total_dim = 234
        elif app_name == 'autovehicle':
            print("   车辆状态: 10个时间步, 每个4维 = 40维")
            print("   控制输入: 9个时间步, 每个2维 = 18维")
            total_dim = 58
        elif app_name == 'quadrotor':
            print("   飞行状态: 10个时间步, 每个12维 = 120维")
            print("   控制输入: 9个时间步, 每个4维 = 36维")
            total_dim = 156
        
        print(f"   总变量维度: {total_dim}")
        print()
        
        # 控制算法的动力学写入
        print("📝 控制动力学的具体写入:")
        print("1️⃣ 动力学约束: xi+1 = f(xi, ui)")
        print("2️⃣ 代价函数: ||xi - xref||² + ||ui||²")
        print("3️⃣ 状态约束: xmin ≤ xi ≤ xmax")
        
        dynamics_writes = total_dim * 2  # 动力学和代价
        total_factors = len(factors)
        
        print(f"   每因子动力学写入: {dynamics_writes} 个浮点数")
        print(f"   总动力学写入: {dynamics_writes * total_factors:,}")
        print()
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()


def main():
    """主函数 - 详细分析写入内容"""
    
    analyzer = DetailedWriteContentAnalyzer()
    
    print("🚀 详细分析RRAM写入的具体内容")
    print("=" * 80)
    print("基于您的重要指正:")
    print("1. 仔细说明都在写什么内容")
    print("2. 使用复杂的SLAM误差公式 (如您图片所示)")
    print("3. 修正机械臂的变量维度 (不是1维)")
    print()
    
    # 分析几个代表性算法
    representative_algorithms = [
        ('robot', 'localization'),
        ('manipulator', 'localization'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]
    
    for app_name, algorithm in representative_algorithms:
        try:
            analyzer.analyze_detailed_write_content(app_name, algorithm)
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")
    
    print("🎯 关键修正总结:")
    print("1. 误差公式使用复杂的SLAM公式，不是简单的e=h(x)-z")
    print("2. 机械臂定位是66维，不是1维")
    print("3. 写入内容包括旋转矩阵、Jacobian、Hessian等复杂数据")
    print("4. 变量维度从几十维到几百维不等")


if __name__ == "__main__":
    main()
