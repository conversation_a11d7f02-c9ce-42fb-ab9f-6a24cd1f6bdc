#!/usr/bin/env python3
"""
基于论文Table 2的正确因子特定分析

重要修正：
1. 不是所有因子都用SLAM旋转+平移公式
2. 每种因子类型有不同的误差公式和操作过程
3. 基于具体公式建立operation过程
4. 统计所有被遗漏的写入操作
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class CorrectFactorSpecificAnalyzer:
    """基于论文Table 2的正确因子分析"""
    
    def __init__(self):
        # 基于论文Table 2的因子类型和公式
        self.factor_formulas = {
            # Measurement因子
            'LiDAR': {
                'formula': 'f(xi, xj) = (xi ⊖ xj) ⊖ zij',
                'error_formula': 'ξ1 ⊕ ξ2 = -Log(R1 R2), t1 + R1 t2',
                'variables': ['xi', 'xj', 'zij'],
                'operations': [
                    ('Store', 'xi', 6),  # 6DOF位姿
                    ('Store', 'xj', 6),  # 6DOF位姿  
                    ('Store', 'zij', 6), # 观测值
                    ('Exp', 'R1 = Exp(φ1)', 9),  # 旋转矩阵
                    ('Exp', 'R2 = Exp(φ2)', 9),  # 旋转矩阵
                    ('DynamicVMM', 'R1 * R2', 9),  # 矩阵乘法
                    ('LOG', '-Log(R1 R2)', 3),  # 对数映射
                    ('VP', 't1 + R1*t2', 3),  # 向量运算
                    ('StaticVMM', 'Jacobian计算', 36),  # 6×6 Jacobian
                ],
                'total_writes_per_factor': 87
            },
            
            'GPS': {
                'formula': 'f(x) = h(x) - z',
                'error_formula': 'e = position(x) - gps_measurement',
                'variables': ['x', 'z'],
                'operations': [
                    ('Store', 'x', 3),  # 位置
                    ('Store', 'z', 3),  # GPS测量
                    ('VP', 'position(x)', 3),  # 位置提取
                    ('VP', 'e = pos - z', 3),  # 误差计算
                    ('StaticVMM', 'Jacobian计算', 9),  # 3×3 Jacobian
                ],
                'total_writes_per_factor': 21
            },
            
            'IMU': {
                'formula': 'f(xi, xi+1, vi, bi) = IMU_model',
                'error_formula': 'e = [eω, ea] = [ω - (ωm - bω), a - (am - ba)]',
                'variables': ['xi', 'xi+1', 'vi', 'bi', 'ωm', 'am'],
                'operations': [
                    ('Store', 'xi', 6),  # 当前位姿
                    ('Store', 'xi+1', 6),  # 下一位姿
                    ('Store', 'vi', 3),  # 速度
                    ('Store', 'bi', 6),  # 偏置
                    ('Store', 'ωm', 3),  # 角速度测量
                    ('Store', 'am', 3),  # 加速度测量
                    ('VP', 'ω - (ωm - bω)', 3),  # 角速度误差
                    ('VP', 'a - (am - ba)', 3),  # 加速度误差
                    ('StaticVMM', 'Jacobian计算', 108),  # 6×18 Jacobian
                ],
                'total_writes_per_factor': 141
            },
            
            'Prior': {
                'formula': 'f(x) = x - x0',
                'error_formula': 'e = x - prior_value',
                'variables': ['x', 'x0'],
                'operations': [
                    ('Store', 'x', 'variable_dim'),  # 变量维度
                    ('Store', 'x0', 'variable_dim'),  # 先验值
                    ('VP', 'e = x - x0', 'variable_dim'),  # 误差
                    ('StaticVMM', 'Jacobian = I', 'variable_dim^2'),  # 单位矩阵
                ],
                'total_writes_per_factor': 'depends_on_variable_dim'
            },
            
            # Constraint因子
            'Smooth': {
                'formula': 'f(xi, xi+1) = ||xi+1 - xi||²',
                'error_formula': 'e = xi+1 - xi',
                'variables': ['xi', 'xi+1'],
                'operations': [
                    ('Store', 'xi', 'variable_dim'),
                    ('Store', 'xi+1', 'variable_dim'),
                    ('VP', 'e = xi+1 - xi', 'variable_dim'),
                    ('StaticVMM', 'Jacobian计算', 'variable_dim * 2 * variable_dim'),
                ],
                'total_writes_per_factor': 'depends_on_variable_dim'
            },
            
            'Collision-free': {
                'formula': 'f(x) = distance(x, obstacles) - threshold',
                'error_formula': 'e = d_min - d(x, obs)',
                'variables': ['x', 'obstacles'],
                'operations': [
                    ('Store', 'x', 'variable_dim'),
                    ('Store', 'obstacles', 'num_obstacles * 3'),
                    ('DynamicVMM', 'distance计算', 'num_obstacles'),
                    ('VP', 'min distance', 1),
                    ('VP', 'e = d_min - threshold', 1),
                    ('StaticVMM', 'Jacobian计算', 'variable_dim'),
                ],
                'total_writes_per_factor': 'depends_on_obstacles'
            },
            
            'Kinematics': {
                'formula': 'f(xi, ui, xi+1) = xi+1 - kinematic_model(xi, ui)',
                'error_formula': 'e = xi+1 - f_kin(xi, ui)',
                'variables': ['xi', 'ui', 'xi+1'],
                'operations': [
                    ('Store', 'xi', 'state_dim'),
                    ('Store', 'ui', 'control_dim'),
                    ('Store', 'xi+1', 'state_dim'),
                    ('DynamicVMM', 'kinematic_model', 'state_dim'),
                    ('VP', 'e = xi+1 - f_kin', 'state_dim'),
                    ('StaticVMM', 'Jacobian计算', 'state_dim * (2*state_dim + control_dim)'),
                ],
                'total_writes_per_factor': 'depends_on_state_control_dim'
            },
            
            'Dynamics': {
                'formula': 'f(xi, ui, xi+1) = xi+1 - dynamic_model(xi, ui)',
                'error_formula': 'e = xi+1 - f_dyn(xi, ui)',
                'variables': ['xi', 'ui', 'xi+1'],
                'operations': [
                    ('Store', 'xi', 'state_dim'),
                    ('Store', 'ui', 'control_dim'),
                    ('Store', 'xi+1', 'state_dim'),
                    ('DynamicVMM', 'dynamic_model', 'state_dim * 2'),  # 更复杂
                    ('VP', 'e = xi+1 - f_dyn', 'state_dim'),
                    ('StaticVMM', 'Jacobian计算', 'state_dim * (2*state_dim + control_dim)'),
                ],
                'total_writes_per_factor': 'depends_on_state_control_dim'
            }
        }
        
        # 基于表格的变量维度
        self.table_variable_dimensions = {
            'robot_localization': 3,
            'robot_planning': 6,
            'robot_control': (3, 2),
            'manipulator_localization': 2,
            'manipulator_planning': 4,
            'manipulator_control': (2, 2),
            'autovehicle_localization': 3,
            'autovehicle_planning': 6,
            'autovehicle_control': (5, 2),
            'quadrotor_localization': 6,
            'quadrotor_planning': 12,
            'quadrotor_control': (12, 5),
        }
        
        # 算法到因子类型的映射 (基于论文Table 2)
        self.algorithm_factor_mapping = {
            'robot_localization': ['LiDAR', 'GPS'],
            'robot_planning': ['Smooth', 'Collision-free'],
            'robot_control': ['Dynamics'],
            'manipulator_localization': ['Prior'],
            'manipulator_planning': ['Smooth', 'Collision-free'],
            'manipulator_control': ['Dynamics'],
            'autovehicle_localization': ['LiDAR', 'GPS'],
            'autovehicle_planning': ['Smooth', 'Collision-free', 'Kinematics'],
            'autovehicle_control': ['Kinematics', 'Dynamics'],
            'quadrotor_localization': ['IMU', 'Prior'],  # Camera用类似LiDAR
            'quadrotor_planning': ['Smooth', 'Collision-free', 'Kinematics'],
            'quadrotor_control': ['Kinematics', 'Dynamics'],
        }
    
    def analyze_algorithm_by_factors(self, app_name: str, algorithm: str):
        """基于具体因子类型分析算法"""
        
        print(f"\n🔍 基于因子类型分析: {app_name} - {algorithm}")
        print("=" * 80)
        
        # 获取因子图结构
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        # 获取算法的因子类型
        algorithm_key = f'{app_name}_{algorithm}'
        factor_types = self.algorithm_factor_mapping.get(algorithm_key, [])
        variable_dim = self.table_variable_dimensions.get(algorithm_key, 0)
        
        print(f"📊 算法信息:")
        print(f"   变量维度: {variable_dim}")
        print(f"   因子类型: {factor_types}")
        print(f"   因子总数: {len(factors)}")
        print()
        
        # 分析每种因子类型的写入
        total_writes = 0
        total_missing_writes = 0
        
        for factor_type in factor_types:
            writes, missing = self._analyze_factor_type_writes(
                factor_type, variable_dim, len(factors))
            total_writes += writes
            total_missing_writes += missing
            
        print(f"🎯 {app_name} - {algorithm} 总结:")
        print(f"   总RRAM写入: {total_writes:,}")
        print(f"   之前遗漏的写入: {total_missing_writes:,}")
        print(f"   修正比例: {(total_missing_writes/max(total_writes,1))*100:.1f}%")
        print()
        
        return total_writes
    
    def _analyze_factor_type_writes(self, factor_type: str, variable_dim, num_factors: int):
        """分析特定因子类型的写入"""
        
        print(f"📋 {factor_type} 因子分析:")
        
        factor_info = self.factor_formulas.get(factor_type, {})
        formula = factor_info.get('formula', '')
        error_formula = factor_info.get('error_formula', '')
        operations = factor_info.get('operations', [])
        
        print(f"   数学公式: {formula}")
        print(f"   误差公式: {error_formula}")
        print(f"   具体操作过程:")
        
        total_writes_per_factor = 0
        missing_writes = 0
        
        for op_type, description, writes in operations:
            if isinstance(writes, str):
                # 处理依赖变量维度的情况
                if writes == 'variable_dim':
                    if isinstance(variable_dim, tuple):
                        actual_writes = sum(variable_dim)
                    else:
                        actual_writes = variable_dim
                elif writes == 'variable_dim^2':
                    if isinstance(variable_dim, tuple):
                        actual_writes = sum(variable_dim) ** 2
                    else:
                        actual_writes = variable_dim ** 2
                elif 'state_dim' in writes or 'control_dim' in writes:
                    if isinstance(variable_dim, tuple):
                        state_dim, control_dim = variable_dim
                        actual_writes = eval(writes.replace('state_dim', str(state_dim))
                                           .replace('control_dim', str(control_dim)))
                    else:
                        actual_writes = variable_dim * 2  # 估算
                else:
                    actual_writes = 10  # 默认估算
            else:
                actual_writes = writes
            
            # 判断是否需要RRAM写入
            needs_rram = op_type in ['Store', 'StaticVMM', 'DynamicVMM']
            rram_status = "RRAM写入" if needs_rram else "SIMD计算"
            
            print(f"      {op_type}: {description} - {actual_writes} 次 ({rram_status})")
            
            if needs_rram:
                total_writes_per_factor += actual_writes
            else:
                missing_writes += actual_writes  # 之前可能遗漏的
        
        total_writes = total_writes_per_factor * num_factors
        total_missing = missing_writes * num_factors
        
        print(f"   每因子RRAM写入: {total_writes_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   该类型总写入: {total_writes:,}")
        print(f"   遗漏的SIMD操作: {total_missing:,}")
        print()
        
        return total_writes, total_missing
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()


def main():
    """主函数 - 基于因子类型的正确分析"""
    
    analyzer = CorrectFactorSpecificAnalyzer()
    
    print("🚀 基于论文Table 2的正确因子特定分析")
    print("=" * 80)
    print("重要修正:")
    print("1. 不是所有因子都用SLAM旋转+平移公式")
    print("2. 每种因子类型有不同的误差公式")
    print("3. 基于具体公式建立operation过程")
    print("4. 统计所有被遗漏的写入操作")
    print()
    
    # 分析所有12个算法
    algorithms = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('manipulator', 'localization'),
        ('manipulator', 'planning'),
        ('manipulator', 'control'),
        ('autovehicle', 'localization'),
        ('autovehicle', 'planning'),
        ('autovehicle', 'control'),
        ('quadrotor', 'localization'),
        ('quadrotor', 'planning'),
        ('quadrotor', 'control'),
    ]
    
    total_all_writes = 0
    
    for app_name, algorithm in algorithms:
        try:
            writes = analyzer.analyze_algorithm_by_factors(app_name, algorithm)
            total_all_writes += writes
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")
    
    print(f"🎯 总结")
    print("=" * 80)
    print(f"12个算法总RRAM写入: {total_all_writes:,}")
    print(f"平均每算法: {total_all_writes//12:,}")
    print()
    print("关键修正:")
    print("1. 基于论文Table 2的真实因子类型")
    print("2. 每种因子有不同的数学公式和操作过程")
    print("3. 统计了之前遗漏的SIMD操作")
    print("4. 提供了准确的硬件设计数据")


if __name__ == "__main__":
    main()
