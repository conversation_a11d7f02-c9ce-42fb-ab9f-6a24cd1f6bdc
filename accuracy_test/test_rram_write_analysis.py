#!/usr/bin/env python3
"""
Test script for RRAM Write Count Analysis

This script demonstrates how to use the RRAMWriteCountAnalyzer to test
RRAM write operations when converting factor graph applications to 
SLAM configurations.

Usage:
    python test_rram_write_analysis.py
    python test_rram_write_analysis.py --app robot --algorithm localization
    python test_rram_write_analysis.py --cycles 1000 --compare
"""

import argparse
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rram_write_count_analyzer import RRAMWriteCountAnalyzer

def test_single_application(app_name: str, algorithm: str, cycles: int = 100):
    """Test a single application configuration"""
    
    print(f"🧪 Testing single application: {app_name} - {algorithm}")
    
    analyzer = RRAMWriteCountAnalyzer()
    
    try:
        # Analyze RRAM writes
        stats = analyzer.analyze_application_config(
            app_name=app_name,
            algorithm=algorithm,
            execution_cycles=cycles
        )
        
        # Print detailed results
        analyzer.print_detailed_analysis(stats, app_name, algorithm)
        
        # Key insights
        print(f"\n🔍 KEY INSIGHTS:")
        print(f"  • Each execution cycle writes {stats.total_cells_written // cycles:,} RRAM cells")
        print(f"  • WriteWeights operations: {stats.explicit_write_weights // cycles} per cycle")
        print(f"  • Matrix reuse efficiency: {stats.reused_matrices}/{stats.unique_matrices} types reused")
        
        return stats
        
    except Exception as e:
        print(f"❌ Error testing {app_name} - {algorithm}: {e}")
        return None

def test_configuration_comparison(app_name: str, algorithm: str, cycles: int = 100):
    """Test and compare different SLAM configurations"""
    
    print(f"⚖️  Comparing SLAM configurations for: {app_name} - {algorithm}")
    
    analyzer = RRAMWriteCountAnalyzer()
    
    try:
        results = analyzer.compare_configurations(
            app_name=app_name,
            algorithm=algorithm,
            execution_cycles=cycles
        )
        
        if results:
            print(f"\n✅ Configuration comparison completed")
            return results
        else:
            print(f"❌ No configurations could be analyzed")
            return None
            
    except Exception as e:
        print(f"❌ Error in configuration comparison: {e}")
        return None

def test_all_applications(cycles: int = 50):
    """Test all available applications with reduced cycles for quick overview"""
    
    print(f"🌐 Testing all applications (quick overview with {cycles} cycles)")
    
    applications = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('manipulator', 'localization'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]
    
    analyzer = RRAMWriteCountAnalyzer()
    results_summary = []
    
    for app_name, algorithm in applications:
        try:
            print(f"\n📋 Quick test: {app_name} - {algorithm}")
            
            stats = analyzer.analyze_application_config(
                app_name=app_name,
                algorithm=algorithm,
                execution_cycles=cycles
            )
            
            # Store summary info
            results_summary.append({
                'app': f"{app_name}_{algorithm}",
                'total_ops': stats.total_write_operations,
                'total_cells': stats.total_cells_written,
                'cells_per_cycle': stats.total_cells_written // cycles,
                'writeweights_ops': stats.explicit_write_weights,
                'max_matrix': f"{stats.max_matrix_size[0]}x{stats.max_matrix_size[1]}"
            })
            
            print(f"  ✓ {stats.total_cells_written:,} cells written, "
                  f"{stats.total_write_operations} operations")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
            results_summary.append({
                'app': f"{app_name}_{algorithm}",
                'error': str(e)
            })
    
    # Print summary table
    print(f"\n{'='*100}")
    print(f"SUMMARY TABLE - RRAM WRITE ANALYSIS")
    print(f"{'='*100}")
    
    print(f"{'Application':<25} {'Total Ops':<12} {'Total Cells':<15} "
          f"{'Cells/Cycle':<12} {'WriteWeights':<12} {'Max Matrix':<10}")
    print(f"{'-'*100}")
    
    for result in results_summary:
        if 'error' not in result:
            print(f"{result['app']:<25} {result['total_ops']:<12,} "
                  f"{result['total_cells']:<15,} {result['cells_per_cycle']:<12,} "
                  f"{result['writeweights_ops']:<12,} {result['max_matrix']:<10}")
        else:
            print(f"{result['app']:<25} {'ERROR':<12} {result['error'][:50]:<50}")

def main():
    """Main function with command-line interface"""
    
    parser = argparse.ArgumentParser(
        description="Test RRAM write operations for factor graph applications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_rram_write_analysis.py
  python test_rram_write_analysis.py --app robot --algorithm localization
  python test_rram_write_analysis.py --app autovehicle --algorithm localization --cycles 1000
  python test_rram_write_analysis.py --compare --app robot --algorithm localization
  python test_rram_write_analysis.py --all
        """
    )
    
    parser.add_argument('--app', type=str, 
                       choices=['robot', 'manipulator', 'autovehicle', 'quadrotor'],
                       help='Application to test')
    parser.add_argument('--algorithm', type=str,
                       choices=['localization', 'planning', 'control'],
                       help='Algorithm to test')
    parser.add_argument('--cycles', type=int, default=100,
                       help='Number of execution cycles to simulate (default: 100)')
    parser.add_argument('--compare', action='store_true',
                       help='Compare different SLAM configurations')
    parser.add_argument('--all', action='store_true',
                       help='Test all applications (quick overview)')
    
    args = parser.parse_args()
    
    print("🔬 RRAM Write Count Analysis Tool")
    print("=" * 50)
    
    if args.all:
        # Test all applications
        test_all_applications(cycles=args.cycles)
        
    elif args.app and args.algorithm:
        # Test specific application
        if args.compare:
            test_configuration_comparison(args.app, args.algorithm, args.cycles)
        else:
            test_single_application(args.app, args.algorithm, args.cycles)
            
    else:
        # Default: test a representative application
        print("No specific application specified. Testing default: robot localization")
        test_single_application('robot', 'localization', args.cycles)
        
        print(f"\n💡 TIP: Use --help to see all available options")
        print(f"💡 TIP: Try --all for a quick overview of all applications")

if __name__ == "__main__":
    main()
