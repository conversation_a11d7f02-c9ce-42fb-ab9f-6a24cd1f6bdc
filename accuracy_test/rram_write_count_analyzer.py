#!/usr/bin/env python3
"""
RRAM Write Count Analyzer for Factor Graph Applications

This tool analyzes RRAM write operations when converting factor graph applications
from factorgraph/application to slam_factorgraph configurations.

Key Analysis Points:
1. Static vs Dynamic write operations
2. WriteWeights operations count
3. Matrix reuse patterns
4. Total RRAM cell write frequency

Author: Event-Driven-Simulator Team
"""

import sys
import os
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import json
import time
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mapping_scheduling.configs import slam_factorgraph_ACIM, slam_factorgraph
    from mapping_scheduling.op_list import Operator, OpList
    from factorgraph.application.application import *
    print("Successfully imported required modules for RRAM write analysis.")
except ImportError as e:
    print(f"Error importing modules: {e}")
    raise

@dataclass
class WriteOperation:
    """Represents a single RRAM write operation"""
    operation_name: str
    operation_type: str  # 'WriteWeights', 'StaticVMM', 'DynamicVMM'
    matrix_size: Tuple[int, int]
    total_cells: int
    copy_time: int
    concurrent_tag: str
    reuse_count: int = 1  # How many times this matrix is reused

@dataclass
class RRAMWriteStats:
    """Statistics for RRAM write operations"""
    total_write_operations: int
    total_cells_written: int
    unique_matrices: int
    reused_matrices: int
    static_vmm_writes: int
    dynamic_vmm_writes: int
    explicit_write_weights: int
    max_matrix_size: Tuple[int, int]
    write_frequency_distribution: Dict[str, int]

class RRAMWriteCountAnalyzer:
    """
    Analyzes RRAM write operations for factor graph applications
    """
    
    def __init__(self):
        self.write_operations: List[WriteOperation] = []
        self.matrix_reuse_tracker: Dict[str, int] = defaultdict(int)
        self.operation_dependencies: Dict[str, List[str]] = {}
        self.total_execution_cycles = 1  # Default single execution
        
    def analyze_application_config(self, app_name: str, algorithm: str, 
                                 config_module=slam_factorgraph_ACIM,
                                 execution_cycles: int = 1) -> RRAMWriteStats:
        """
        Analyze RRAM write operations for a specific application configuration
        
        Args:
            app_name: Application name ('robot', 'manipulator', 'autovehicle', 'quadrotor')
            algorithm: Algorithm type ('localization', 'planning', 'control')
            config_module: Configuration module to use (slam_factorgraph_ACIM or slam_factorgraph)
            execution_cycles: Number of execution cycles to simulate
            
        Returns:
            RRAMWriteStats object with detailed analysis
        """
        print(f"\n=== Analyzing RRAM writes for {app_name} - {algorithm} ===")
        
        self.total_execution_cycles = execution_cycles
        self.write_operations.clear()
        self.matrix_reuse_tracker.clear()
        
        # Get factor graph structure from application
        factor_graph_info = self._get_factor_graph_info(app_name, algorithm)
        
        # Generate SLAM configuration
        slam_config = self._generate_slam_config(factor_graph_info, config_module)
        
        # Analyze write operations
        self._analyze_write_operations(slam_config)
        
        # Calculate statistics
        stats = self._calculate_statistics()
        
        return stats
    
    def _get_factor_graph_info(self, app_name: str, algorithm: str) -> Dict[str, Any]:
        """Extract factor graph information from application generators"""
        
        # Map application names to generator functions
        generator_map = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator_func = generator_map.get((app_name, algorithm))
        if not generator_func:
            raise ValueError(f"Unknown application: {app_name} - {algorithm}")
        
        nodes, factors, layout = generator_func()
        
        # Calculate complexity metrics
        num_variables = sum(len(node_list) for node_list in nodes.values())
        num_factors = len(factors)
        
        return {
            'nodes': nodes,
            'factors': factors,
            'layout': layout,
            'num_variables': num_variables,
            'num_factors': num_factors,
            'complexity_score': num_variables * num_factors
        }
    
    def _generate_slam_config(self, factor_graph_info: Dict[str, Any], 
                            config_module) -> Tuple[Any, List[Any], Any, Any]:
        """Generate SLAM configuration from factor graph"""
        
        # Estimate parameters based on factor graph complexity
        param = self._estimate_slam_parameters(factor_graph_info)
        
        # Generate configuration
        try:
            scheduling_setting, op_list, input_row_parallel_dict, model_param = \
                config_module.get_scheduling_configs(param=param)
            
            print(f"Generated {len(op_list)} operations for SLAM configuration")
            return scheduling_setting, op_list, input_row_parallel_dict, model_param
            
        except Exception as e:
            print(f"Error generating SLAM config: {e}")
            raise
    
    def _estimate_slam_parameters(self, factor_graph_info: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate SLAM parameters based on factor graph complexity"""
        
        # Base parameters for 3D SLAM
        base_param = {
            'rotation_dim': 3,
            'translation_dim': 3,
            'matrix_dim': 3,
            'M_dim': 12,  # Jacobian width
            'copy_times': 1
        }
        
        # Scale based on factor graph complexity
        complexity = factor_graph_info['complexity_score']
        
        if complexity > 100:  # Large factor graph
            base_param['copy_times'] = max(1, complexity // 50)
        
        return base_param

    def _analyze_write_operations(self, slam_config: Tuple[Any, List[Any], Any, Any]):
        """Analyze write operations from SLAM configuration"""

        scheduling_setting, op_list, input_row_parallel_dict, model_param = slam_config

        print(f"Analyzing {len(op_list)} operations...")

        for op_entry in op_list:
            if isinstance(op_entry, list) and len(op_entry) >= 1:
                operator = op_entry[0]
                dependencies = op_entry[1] if len(op_entry) > 1 else []

                if isinstance(operator, Operator):
                    self._process_operator(operator, dependencies)

    def _process_operator(self, operator: Operator, dependencies: List[Any]):
        """Process a single operator for RRAM write analysis"""

        # Check if this operation requires RRAM writes
        requires_write = self._requires_rram_write(operator)

        if requires_write:
            # Calculate matrix size and total cells
            matrix_size = self._get_matrix_size(operator)
            total_cells = matrix_size[0] * matrix_size[1]

            # Extract copy time from operation name
            copy_time = self._extract_copy_time(operator.name)

            # Track matrix reuse
            matrix_key = f"{operator.type}_{matrix_size[0]}x{matrix_size[1]}"
            self.matrix_reuse_tracker[matrix_key] += 1

            # Create write operation record
            write_op = WriteOperation(
                operation_name=operator.name,
                operation_type=operator.type,
                matrix_size=matrix_size,
                total_cells=total_cells,
                copy_time=copy_time,
                concurrent_tag=getattr(operator, 'concurrent_tag', ''),
                reuse_count=1
            )

            self.write_operations.append(write_op)

            print(f"  RRAM Write: {operator.type} {matrix_size} -> {total_cells} cells")

    def _requires_rram_write(self, operator: Operator) -> bool:
        """Determine if an operator requires RRAM write operations"""

        # Explicit WriteWeights operations
        if operator.type == 'WriteWeights':
            return True

        # StaticVMM operations require weight matrix writes
        if operator.type == 'StaticVMM':
            return True

        # DynamicVMM may require writes depending on implementation
        if operator.type == 'DynamicVMM':
            return True

        # Fused operations
        if operator.type == 'WriteWeights+DynamicVMM':
            return True

        return False

    def _get_matrix_size(self, operator: Operator) -> Tuple[int, int]:
        """Extract matrix size from operator"""

        if hasattr(operator, 'data_size') and hasattr(operator, 'operation_size'):
            # For WriteWeights, use operation_size as it represents the weight matrix
            if operator.type == 'WriteWeights':
                return operator.operation_size

            # For VMM operations, weight matrix size is typically operation_size
            elif operator.type in ['StaticVMM', 'DynamicVMM']:
                return operator.operation_size

            # For fused operations
            elif operator.type == 'WriteWeights+DynamicVMM':
                return operator.operation_size

        # Fallback to data_size if operation_size not available
        if hasattr(operator, 'data_size'):
            return operator.data_size

        # Default small matrix if no size information
        return (1, 1)

    def _extract_copy_time(self, operation_name: str) -> int:
        """Extract copy time from operation name"""

        # Look for pattern like '_0', '_1', etc. at the end
        import re
        match = re.search(r'_(\d+)$', operation_name)
        if match:
            return int(match.group(1))

        return 0

    def _calculate_statistics(self) -> RRAMWriteStats:
        """Calculate comprehensive RRAM write statistics"""

        if not self.write_operations:
            return RRAMWriteStats(
                total_write_operations=0,
                total_cells_written=0,
                unique_matrices=0,
                reused_matrices=0,
                static_vmm_writes=0,
                dynamic_vmm_writes=0,
                explicit_write_weights=0,
                max_matrix_size=(0, 0),
                write_frequency_distribution={}
            )

        # Basic counts
        total_ops = len(self.write_operations)
        total_cells = sum(op.total_cells for op in self.write_operations) * self.total_execution_cycles

        # Operation type counts
        type_counts = Counter(op.operation_type for op in self.write_operations)

        # Matrix size analysis
        matrix_sizes = [op.matrix_size for op in self.write_operations]
        max_size = max(matrix_sizes, key=lambda x: x[0] * x[1])

        # Reuse analysis
        unique_matrices = len(self.matrix_reuse_tracker)
        reused_matrices = sum(1 for count in self.matrix_reuse_tracker.values() if count > 1)

        # Frequency distribution
        freq_dist = dict(Counter(f"{op.operation_type}_{op.matrix_size[0]}x{op.matrix_size[1]}"
                               for op in self.write_operations))

        return RRAMWriteStats(
            total_write_operations=total_ops * self.total_execution_cycles,
            total_cells_written=total_cells,
            unique_matrices=unique_matrices,
            reused_matrices=reused_matrices,
            static_vmm_writes=type_counts.get('StaticVMM', 0) * self.total_execution_cycles,
            dynamic_vmm_writes=type_counts.get('DynamicVMM', 0) * self.total_execution_cycles,
            explicit_write_weights=type_counts.get('WriteWeights', 0) * self.total_execution_cycles,
            max_matrix_size=max_size,
            write_frequency_distribution=freq_dist
        )

    def print_detailed_analysis(self, stats: RRAMWriteStats, app_name: str, algorithm: str):
        """Print detailed analysis results"""

        print(f"\n{'='*80}")
        print(f"RRAM Write Analysis Results: {app_name.upper()} - {algorithm.upper()}")
        print(f"{'='*80}")

        print(f"\n📊 OPERATION STATISTICS:")
        print(f"  Total Write Operations:     {stats.total_write_operations:,}")
        print(f"  Total RRAM Cells Written:   {stats.total_cells_written:,}")
        print(f"  Execution Cycles:           {self.total_execution_cycles:,}")

        print(f"\n🔧 OPERATION TYPE BREAKDOWN:")
        print(f"  Explicit WriteWeights:      {stats.explicit_write_weights:,}")
        print(f"  StaticVMM (weight writes):  {stats.static_vmm_writes:,}")
        print(f"  DynamicVMM (weight writes): {stats.dynamic_vmm_writes:,}")

        print(f"\n📐 MATRIX ANALYSIS:")
        print(f"  Unique Matrix Types:        {stats.unique_matrices}")
        print(f"  Reused Matrix Types:        {stats.reused_matrices}")
        print(f"  Largest Matrix Size:        {stats.max_matrix_size[0]}x{stats.max_matrix_size[1]}")
        print(f"  Largest Matrix Cells:       {stats.max_matrix_size[0] * stats.max_matrix_size[1]:,}")

        print(f"\n📈 WRITE FREQUENCY DISTRIBUTION:")
        sorted_freq = sorted(stats.write_frequency_distribution.items(),
                           key=lambda x: x[1], reverse=True)
        for op_type, count in sorted_freq[:10]:  # Top 10 most frequent
            print(f"  {op_type:<30}: {count:,} operations")

        if len(sorted_freq) > 10:
            print(f"  ... and {len(sorted_freq) - 10} more operation types")

        # Calculate write intensity
        avg_cells_per_op = stats.total_cells_written / max(stats.total_write_operations, 1)
        print(f"\n⚡ WRITE INTENSITY:")
        print(f"  Average cells per operation: {avg_cells_per_op:.1f}")
        print(f"  Write density (cells/cycle):  {stats.total_cells_written / max(self.total_execution_cycles, 1):,.1f}")

    def compare_configurations(self, app_name: str, algorithm: str,
                             execution_cycles: int = 100) -> Dict[str, RRAMWriteStats]:
        """Compare RRAM writes between different SLAM configurations"""

        print(f"\n🔄 COMPARING SLAM CONFIGURATIONS for {app_name} - {algorithm}")

        results = {}

        # Test ACIM configuration
        try:
            stats_acim = self.analyze_application_config(
                app_name, algorithm, slam_factorgraph_ACIM, execution_cycles
            )
            results['ACIM'] = stats_acim
            print(f"✅ ACIM configuration analyzed")
        except Exception as e:
            print(f"❌ ACIM configuration failed: {e}")

        # Test standard configuration
        try:
            stats_standard = self.analyze_application_config(
                app_name, algorithm, slam_factorgraph, execution_cycles
            )
            results['Standard'] = stats_standard
            print(f"✅ Standard configuration analyzed")
        except Exception as e:
            print(f"❌ Standard configuration failed: {e}")

        # Print comparison
        if len(results) > 1:
            self._print_configuration_comparison(results)

        return results

    def _print_configuration_comparison(self, results: Dict[str, RRAMWriteStats]):
        """Print comparison between different configurations"""

        print(f"\n{'='*80}")
        print(f"CONFIGURATION COMPARISON")
        print(f"{'='*80}")

        configs = list(results.keys())

        print(f"\n{'Metric':<30} {'ACIM':<15} {'Standard':<15} {'Difference':<15}")
        print(f"{'-'*75}")

        for metric_name, metric_func in [
            ('Total Write Ops', lambda s: s.total_write_operations),
            ('Total Cells Written', lambda s: s.total_cells_written),
            ('WriteWeights Ops', lambda s: s.explicit_write_weights),
            ('StaticVMM Ops', lambda s: s.static_vmm_writes),
            ('DynamicVMM Ops', lambda s: s.dynamic_vmm_writes),
            ('Unique Matrices', lambda s: s.unique_matrices),
        ]:
            values = [metric_func(results[config]) for config in configs]

            if len(values) >= 2:
                diff = values[0] - values[1]  # ACIM - Standard
                diff_pct = (diff / max(values[1], 1)) * 100

                print(f"{metric_name:<30} {values[0]:<15,} {values[1]:<15,} "
                      f"{diff:+,} ({diff_pct:+.1f}%)")


def main():
    """Main function for command-line usage"""

    analyzer = RRAMWriteCountAnalyzer()

    # Test all applications
    applications = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]

    execution_cycles = 100  # Simulate 100 execution cycles

    print("🚀 Starting RRAM Write Count Analysis for Factor Graph Applications")
    print(f"Simulating {execution_cycles} execution cycles per application")

    all_results = {}

    for app_name, algorithm in applications:
        try:
            print(f"\n{'='*60}")
            print(f"Testing: {app_name} - {algorithm}")

            # Analyze with ACIM configuration
            stats = analyzer.analyze_application_config(
                app_name, algorithm, slam_factorgraph_ACIM, execution_cycles
            )

            analyzer.print_detailed_analysis(stats, app_name, algorithm)

            all_results[f"{app_name}_{algorithm}"] = stats

        except Exception as e:
            print(f"❌ Error analyzing {app_name} - {algorithm}: {e}")

    # Summary across all applications
    print(f"\n{'='*80}")
    print(f"SUMMARY ACROSS ALL APPLICATIONS")
    print(f"{'='*80}")

    total_ops = sum(stats.total_write_operations for stats in all_results.values())
    total_cells = sum(stats.total_cells_written for stats in all_results.values())

    print(f"Total Write Operations:  {total_ops:,}")
    print(f"Total RRAM Cells Written: {total_cells:,}")
    print(f"Average per Application:  {total_cells / len(all_results):,.0f} cells")


if __name__ == "__main__":
    main()
