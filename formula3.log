🚀 完整分析所有12个算法的公式和线性系统构建过程
================================================================================
包含:
1. 每个算法的具体数学公式
2. 线性方程 H * Δx = -g 的完整构建过程
3. 所有RRAM写入操作的详细统计
4. EXP/LOG mapping和Transpose的具体使用


🔍 完整分析: robot - localization
================================================================================
📊 算法信息:
   变量维度: 3
   因子类型: ['LiDAR', 'GPS']
   因子总数: 212

📋 数学公式详解:
------------------------------------------------------------

🔹 LiDAR 因子:
   误差函数: e = (xi ⊖ xj) ⊖ zij
   详细公式: e = -Log(R_ij^T * R_i^T * R_j) + t_ij - R_i^T(t_j - t_i)
   变量定义: xi ∈ SE(3), xj ∈ SE(3), zij ∈ se(3)

   具体操作过程:
      Store: xi (6DOF pose) - 6 次RRAM写入
      Store: xj (6DOF pose) - 6 次RRAM写入
      Store: zij (measurement) - 6 次RRAM写入
      Exp: R_i = Exp(φ_i) - 9 次RRAM写入
      Exp: R_j = Exp(φ_j) - 9 次RRAM写入
      Transpose: R_i^T - 9 次RRAM写入
      StaticVMM: R_i^T * R_j - 9 次RRAM写入
      Transpose: R_ij^T - 9 次RRAM写入
      StaticVMM: R_ij^T * (R_i^T * R_j) - 9 次RRAM写入
      Log: Log(result) - 3 次RRAM写入
      VP: t_ij - R_i^T(t_j - t_i) - 6 次RRAM写入
      Jacobian_SE3: ∂e/∂xi - 18 次RRAM写入
      Jacobian_SE3: ∂e/∂xj - 18 次RRAM写入
   该因子类型每次写入: 117


🔹 GPS 因子:
   误差函数: e = h(x) - z
   详细公式: e = position(x) - gps_measurement
   变量定义: x ∈ SE(3), z ∈ R³

   具体操作过程:
      Store: x (pose) - 6 次RRAM写入
      Store: z (GPS) - 3 次RRAM写入
      Extract: position(x) - 3 次RRAM写入
      VP: e = pos - z - 3 次RRAM写入
      Jacobian_Position: ∂pos/∂x - 18 次RRAM写入
   该因子类型每次写入: 33


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 27 次写入
2️⃣ 转置J^T: 9 次写入
3️⃣ 计算J^T * Ω: 9 次写入
4️⃣ 计算(J^T * Ω) * J: 9 次写入
5️⃣ 累加到H: 9 次写入

🎯 Hessian构建总结:
   每因子写入: 63
   因子数量: 212
   Hessian总写入: 13,356

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 48 次写入
2️⃣ 计算J^T * Ω * e: 3 次写入
3️⃣ 累加到g: 3 次写入

🎯 梯度构建总结:
   每因子写入: 54
   因子数量: 212
   梯度总写入: 11,448

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 4

2️⃣ 前向替换: L * y = -g
   前向替换写入: 4

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 4

🎯 系统求解总写入: 12

🎯 总RRAM写入统计:
   因子操作: 31,800
   Hessian额外: 7,632
   梯度额外: 1,272
   系统求解: 13
   总计: 40,717


🔍 完整分析: robot - planning
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['Smooth', 'Collision-free']
   因子总数: 52

📋 数学公式详解:
------------------------------------------------------------

🔹 Smooth 因子:
   误差函数: e = ||xi+1 - xi||²
   详细公式: e = (xi+1 - xi)^T * Q * (xi+1 - xi)
   变量定义: xi ∈ R⁶, xi+1 ∈ R⁶, Q ∈ R⁶ˣ⁶

   具体操作过程:
      Store: xi - 6 次RRAM写入
      Store: xi+1 - 6 次RRAM写入
      VP: diff = xi+1 - xi - 6 次RRAM写入
      StaticVMM: Q * diff - 6 次RRAM写入
      VP: diff^T * (Q * diff) - 1 次RRAM写入
      Jacobian_Linear: ∂e/∂xi - 6 次RRAM写入
      Jacobian_Linear: ∂e/∂xi+1 - 6 次RRAM写入
   该因子类型每次写入: 37


🔹 Collision-free 因子:
   误差函数: e = max(0, threshold - distance(x, obstacles))
   详细公式: e = max(0, d_safe - min_i(||x - obs_i||))
   变量定义: x ∈ R⁶, obstacles ∈ R^(n×3)

   具体操作过程:
      Store: x - 6 次RRAM写入
      Store: obstacles - 30 次RRAM写入
      DynamicVMM: distances to all obstacles - 10 次RRAM写入
      Min: min distance - 1 次RRAM写入
      VP: e = d_safe - d_min - 1 次RRAM写入
      Jacobian_Distance: ∂d/∂x - 6 次RRAM写入
   该因子类型每次写入: 54


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 9 次写入
2️⃣ 转置J^T: 18 次写入
3️⃣ 计算J^T * Ω: 18 次写入
4️⃣ 计算(J^T * Ω) * J: 36 次写入
5️⃣ 累加到H: 36 次写入

🎯 Hessian构建总结:
   每因子写入: 117
   因子数量: 52
   Hessian总写入: 6,084

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 36 次写入
2️⃣ 计算J^T * Ω * e: 6 次写入
3️⃣ 累加到g: 6 次写入

🎯 梯度构建总结:
   每因子写入: 48
   因子数量: 52
   梯度总写入: 2,496

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 36

2️⃣ 前向替换: L * y = -g
   前向替换写入: 18

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 18

🎯 系统求解总写入: 72

🎯 总RRAM写入统计:
   因子操作: 4,732
   Hessian额外: 5,616
   梯度额外: 624
   系统求解: 72
   总计: 11,044


🔍 完整分析: robot - control
================================================================================
📊 算法信息:
   变量维度: 状态3维, 控制2维
   因子类型: ['Dynamics']
   因子总数: 61

📋 数学公式详解:
------------------------------------------------------------

🔹 Dynamics 因子:
   误差函数: e = xi+1 - f(xi, ui)
   详细公式: e = xi+1 - (A*xi + B*ui + c)
   变量定义: xi ∈ R³, ui ∈ R², xi+1 ∈ R³

   具体操作过程:
      Store: xi - 3 次RRAM写入
      Store: ui - 2 次RRAM写入
      Store: xi+1 - 3 次RRAM写入
      StaticVMM: A * xi - 3 次RRAM写入
      StaticVMM: B * ui - 3 次RRAM写入
      VP: A*xi + B*ui + c - 3 次RRAM写入
      VP: e = xi+1 - f(xi,ui) - 3 次RRAM写入
      Jacobian_State: ∂f/∂xi - 9 次RRAM写入
      Jacobian_Control: ∂f/∂ui - 6 次RRAM写入
   该因子类型每次写入: 35


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 15 次写入
2️⃣ 转置J^T: 9 次写入
3️⃣ 计算J^T * Ω: 9 次写入
4️⃣ 计算(J^T * Ω) * J: 9 次写入
5️⃣ 累加到H: 9 次写入

🎯 Hessian构建总结:
   每因子写入: 51
   因子数量: 61
   Hessian总写入: 3,111

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 20 次写入
2️⃣ 计算J^T * Ω * e: 3 次写入
3️⃣ 累加到g: 3 次写入

🎯 梯度构建总结:
   每因子写入: 26
   因子数量: 61
   梯度总写入: 1,586

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 4

2️⃣ 前向替换: L * y = -g
   前向替换写入: 4

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 4

🎯 系统求解总写入: 12

🎯 总RRAM写入统计:
   因子操作: 2,135
   Hessian额外: 2,196
   梯度额外: 366
   系统求解: 13
   总计: 4,710


🔍 完整分析: manipulator - localization
================================================================================
📊 算法信息:
   变量维度: 2
   因子类型: ['Prior']
   因子总数: 1

📋 数学公式详解:
------------------------------------------------------------

🔹 Prior 因子:
   误差函数: e = x - x0
   详细公式: e = x - prior_mean
   变量定义: x ∈ R², x0 ∈ R²

   具体操作过程:
      Store: x - 2 次RRAM写入
      Store: x0 - 2 次RRAM写入
      VP: e = x - x0 - 2 次RRAM写入
      Jacobian_Identity: ∂e/∂x = I - 4 次RRAM写入
   该因子类型每次写入: 10


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 4 次写入
2️⃣ 转置J^T: 6 次写入
3️⃣ 计算J^T * Ω: 6 次写入
4️⃣ 计算(J^T * Ω) * J: 4 次写入
5️⃣ 累加到H: 4 次写入

🎯 Hessian构建总结:
   每因子写入: 24
   因子数量: 1
   Hessian总写入: 24

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 6 次写入
2️⃣ 计算J^T * Ω * e: 2 次写入
3️⃣ 累加到g: 2 次写入

🎯 梯度构建总结:
   每因子写入: 10
   因子数量: 1
   梯度总写入: 10

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 1

2️⃣ 前向替换: L * y = -g
   前向替换写入: 2

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 2

🎯 系统求解总写入: 5

🎯 总RRAM写入统计:
   因子操作: 10
   Hessian额外: 20
   梯度额外: 4
   系统求解: 5
   总计: 39


🔍 完整分析: manipulator - planning
================================================================================
📊 算法信息:
   变量维度: 4
   因子类型: ['Smooth', 'Collision-free']
   因子总数: 59

📋 数学公式详解:
------------------------------------------------------------

🔹 Smooth 因子:
   误差函数: e = ||qi+1 - qi||²
   详细公式: e = (qi+1 - qi)^T * Q * (qi+1 - qi)
   变量定义: qi ∈ R⁴, qi+1 ∈ R⁴

   具体操作过程:
      Store: qi - 4 次RRAM写入
      Store: qi+1 - 4 次RRAM写入
      VP: diff = qi+1 - qi - 4 次RRAM写入
      StaticVMM: Q * diff - 4 次RRAM写入
      VP: diff^T * (Q * diff) - 1 次RRAM写入
      Jacobian_Linear: ∂e/∂qi - 4 次RRAM写入
      Jacobian_Linear: ∂e/∂qi+1 - 4 次RRAM写入
   该因子类型每次写入: 25


🔹 Collision-free 因子:
   误差函数: e = max(0, threshold - distance(q, obstacles))
   详细公式: e = max(0, d_safe - min_i(||FK(q) - obs_i||))
   变量定义: q ∈ R⁴, obstacles ∈ R^(n×3)

   具体操作过程:
      Store: q - 4 次RRAM写入
      Store: obstacles - 30 次RRAM写入
      ForwardKinematics: FK(q) - 12 次RRAM写入
      DynamicVMM: distances - 10 次RRAM写入
      Min: min distance - 1 次RRAM写入
      VP: e = d_safe - d_min - 1 次RRAM写入
      Jacobian_FK: ∂FK/∂q - 12 次RRAM写入
      Jacobian_Distance: ∂d/∂FK - 3 次RRAM写入
   该因子类型每次写入: 73


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 11 次写入
2️⃣ 转置J^T: 12 次写入
3️⃣ 计算J^T * Ω: 12 次写入
4️⃣ 计算(J^T * Ω) * J: 16 次写入
5️⃣ 累加到H: 16 次写入

🎯 Hessian构建总结:
   每因子写入: 67
   因子数量: 59
   Hessian总写入: 3,953

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 37 次写入
2️⃣ 计算J^T * Ω * e: 4 次写入
3️⃣ 累加到g: 4 次写入

🎯 梯度构建总结:
   每因子写入: 45
   因子数量: 59
   梯度总写入: 2,655

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 10

2️⃣ 前向替换: L * y = -g
   前向替换写入: 8

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 8

🎯 系统求解总写入: 26

🎯 总RRAM写入统计:
   因子操作: 5,782
   Hessian额外: 3,304
   梯度额外: 472
   系统求解: 26
   总计: 9,584


🔍 完整分析: manipulator - control
================================================================================
📊 算法信息:
   变量维度: 状态2维, 控制2维
   因子类型: ['Dynamics']
   因子总数: 61

📋 数学公式详解:
------------------------------------------------------------

🔹 Dynamics 因子:
   误差函数: e = qi+1 - f(qi, τi)
   详细公式: e = qi+1 - (qi + dt * (M^(-1) * (τi - C*qi - G)))
   变量定义: qi ∈ R², τi ∈ R², qi+1 ∈ R²

   具体操作过程:
      Store: qi - 2 次RRAM写入
      Store: τi - 2 次RRAM写入
      Store: qi+1 - 2 次RRAM写入
      DynamicVMM: M(qi) - 4 次RRAM写入
      DynamicVMM: C(qi) - 4 次RRAM写入
      DynamicVMM: G(qi) - 2 次RRAM写入
      Inverse: M^(-1) - 4 次RRAM写入
      StaticVMM: M^(-1) * (τi - C*qi - G) - 2 次RRAM写入
      VP: qi+1_pred = qi + dt * qidot - 2 次RRAM写入
      VP: e = qi+1 - qi+1_pred - 2 次RRAM写入
      Jacobian_Dynamics: ∂f/∂qi - 4 次RRAM写入
      Jacobian_Dynamics: ∂f/∂τi - 4 次RRAM写入
   该因子类型每次写入: 34


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 8 次写入
2️⃣ 转置J^T: 6 次写入
3️⃣ 计算J^T * Ω: 6 次写入
4️⃣ 计算(J^T * Ω) * J: 4 次写入
5️⃣ 累加到H: 4 次写入

🎯 Hessian构建总结:
   每因子写入: 28
   因子数量: 61
   Hessian总写入: 1,708

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 26 次写入
2️⃣ 计算J^T * Ω * e: 2 次写入
3️⃣ 累加到g: 2 次写入

🎯 梯度构建总结:
   每因子写入: 30
   因子数量: 61
   梯度总写入: 1,830

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 1

2️⃣ 前向替换: L * y = -g
   前向替换写入: 2

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 2

🎯 系统求解总写入: 5

🎯 总RRAM写入统计:
   因子操作: 2,074
   Hessian额外: 1,220
   梯度额外: 244
   系统求解: 5
   总计: 3,543


🔍 完整分析: autovehicle - localization
================================================================================
📊 算法信息:
   变量维度: 3
   因子类型: ['LiDAR', 'GPS']
   因子总数: 251

📋 数学公式详解:
------------------------------------------------------------

🔹 LiDAR 因子:
   误差函数: e = (xi ⊖ xj) ⊖ zij
   详细公式: e = -Log(R_ij^T * R_i^T * R_j) + t_ij - R_i^T(t_j - t_i)
   变量定义: xi ∈ SE(3), xj ∈ SE(3), zij ∈ se(3)

   具体操作过程:
      Store: xi (vehicle pose) - 6 次RRAM写入
      Store: xj (landmark) - 6 次RRAM写入
      Store: zij (LiDAR scan) - 6 次RRAM写入
      Exp: R_i = Exp(φ_i) - 9 次RRAM写入
      Exp: R_j = Exp(φ_j) - 9 次RRAM写入
      Transpose: R_i^T - 9 次RRAM写入
      StaticVMM: R_i^T * R_j - 9 次RRAM写入
      Transpose: R_ij^T - 9 次RRAM写入
      StaticVMM: R_ij^T * (R_i^T * R_j) - 9 次RRAM写入
      Log: Log(result) - 3 次RRAM写入
      VP: t_ij - R_i^T(t_j - t_i) - 6 次RRAM写入
      Jacobian_SE3: ∂e/∂xi - 18 次RRAM写入
      Jacobian_SE3: ∂e/∂xj - 18 次RRAM写入
   该因子类型每次写入: 117


🔹 GPS 因子:
   误差函数: e = h(x) - z
   详细公式: e = position(x) - gps_measurement
   变量定义: x ∈ SE(3), z ∈ R³

   具体操作过程:
      Store: x (vehicle pose) - 6 次RRAM写入
      Store: z (GPS) - 3 次RRAM写入
      Extract: position(x) - 3 次RRAM写入
      VP: e = pos - z - 3 次RRAM写入
      Jacobian_Position: ∂pos/∂x - 18 次RRAM写入
   该因子类型每次写入: 33


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 27 次写入
2️⃣ 转置J^T: 9 次写入
3️⃣ 计算J^T * Ω: 9 次写入
4️⃣ 计算(J^T * Ω) * J: 9 次写入
5️⃣ 累加到H: 9 次写入

🎯 Hessian构建总结:
   每因子写入: 63
   因子数量: 251
   Hessian总写入: 15,813

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 48 次写入
2️⃣ 计算J^T * Ω * e: 3 次写入
3️⃣ 累加到g: 3 次写入

🎯 梯度构建总结:
   每因子写入: 54
   因子数量: 251
   梯度总写入: 13,554

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 4

2️⃣ 前向替换: L * y = -g
   前向替换写入: 4

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 4

🎯 系统求解总写入: 12

🎯 总RRAM写入统计:
   因子操作: 37,650
   Hessian额外: 9,036
   梯度额外: 1,506
   系统求解: 13
   总计: 48,205


🔍 完整分析: autovehicle - planning
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['Smooth', 'Collision-free', 'Kinematics']
   因子总数: 17

📋 数学公式详解:
------------------------------------------------------------

🔹 Smooth 因子:
   误差函数: e = ||xi+1 - xi||²
   详细公式: e = (xi+1 - xi)^T * Q * (xi+1 - xi)
   变量定义: xi ∈ R⁶, xi+1 ∈ R⁶

   具体操作过程:
      Store: xi (vehicle state) - 6 次RRAM写入
      Store: xi+1 - 6 次RRAM写入
      VP: diff = xi+1 - xi - 6 次RRAM写入
      StaticVMM: Q * diff - 6 次RRAM写入
      VP: diff^T * (Q * diff) - 1 次RRAM写入
      Jacobian_Linear: ∂e/∂xi - 6 次RRAM写入
      Jacobian_Linear: ∂e/∂xi+1 - 6 次RRAM写入
   该因子类型每次写入: 37


🔹 Collision-free 因子:
   误差函数: e = max(0, threshold - distance(x, obstacles))
   详细公式: e = max(0, d_safe - min_i(||x - obs_i||))
   变量定义: x ∈ R⁶, obstacles ∈ R^(n×3)

   具体操作过程:
      Store: x - 6 次RRAM写入
      Store: obstacles - 30 次RRAM写入
      DynamicVMM: distances - 10 次RRAM写入
      Min: min distance - 1 次RRAM写入
      VP: e = d_safe - d_min - 1 次RRAM写入
      Jacobian_Distance: ∂d/∂x - 6 次RRAM写入
   该因子类型每次写入: 54


🔹 Kinematics 因子:
   误差函数: e = xi+1 - f_kin(xi, ui)
   详细公式: e = xi+1 - bicycle_model(xi, ui)
   变量定义: xi ∈ R⁶, ui ∈ R², xi+1 ∈ R⁶

   具体操作过程:
      Store: xi (x,y,θ,v,φ,a) - 6 次RRAM写入
      Store: ui (δ,a) - 2 次RRAM写入
      Store: xi+1 - 6 次RRAM写入
      Trigonometric: sin(θ), cos(θ) - 2 次RRAM写入
      DynamicVMM: bicycle kinematics - 6 次RRAM写入
      VP: e = xi+1 - f_kin - 6 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂xi - 36 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂ui - 12 次RRAM写入
   该因子类型每次写入: 76


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 22 次写入
2️⃣ 转置J^T: 18 次写入
3️⃣ 计算J^T * Ω: 18 次写入
4️⃣ 计算(J^T * Ω) * J: 36 次写入
5️⃣ 累加到H: 36 次写入

🎯 Hessian构建总结:
   每因子写入: 130
   因子数量: 17
   Hessian总写入: 2,210

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 33 次写入
2️⃣ 计算J^T * Ω * e: 6 次写入
3️⃣ 累加到g: 6 次写入

🎯 梯度构建总结:
   每因子写入: 45
   因子数量: 17
   梯度总写入: 765

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 36

2️⃣ 前向替换: L * y = -g
   前向替换写入: 18

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 18

🎯 系统求解总写入: 72

🎯 总RRAM写入统计:
   因子操作: 2,839
   Hessian额外: 1,836
   梯度额外: 204
   系统求解: 72
   总计: 4,951


🔍 完整分析: autovehicle - control
================================================================================
📊 算法信息:
   变量维度: 状态5维, 控制2维
   因子类型: ['Kinematics', 'Dynamics']
   因子总数: 13

📋 数学公式详解:
------------------------------------------------------------

🔹 Kinematics 因子:
   误差函数: e = xi+1 - f_kin(xi, ui)
   详细公式: e = xi+1 - bicycle_model(xi, ui)
   变量定义: xi ∈ R⁵, ui ∈ R², xi+1 ∈ R⁵

   具体操作过程:
      Store: xi (x,y,θ,v,φ) - 5 次RRAM写入
      Store: ui (δ,a) - 2 次RRAM写入
      Store: xi+1 - 5 次RRAM写入
      Trigonometric: sin(θ), cos(θ) - 2 次RRAM写入
      DynamicVMM: bicycle kinematics - 5 次RRAM写入
      VP: e = xi+1 - f_kin - 5 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂xi - 25 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂ui - 10 次RRAM写入
   该因子类型每次写入: 59


🔹 Dynamics 因子:
   误差函数: e = xi+1 - f_dyn(xi, ui)
   详细公式: e = xi+1 - vehicle_dynamics(xi, ui)
   变量定义: xi ∈ R⁵, ui ∈ R², xi+1 ∈ R⁵

   具体操作过程:
      Store: xi - 5 次RRAM写入
      Store: ui - 2 次RRAM写入
      Store: xi+1 - 5 次RRAM写入
      DynamicVMM: tire forces - 8 次RRAM写入
      DynamicVMM: vehicle dynamics - 10 次RRAM写入
      VP: e = xi+1 - f_dyn - 5 次RRAM写入
      Jacobian_Dynamics: ∂f_dyn/∂xi - 25 次RRAM写入
      Jacobian_Dynamics: ∂f_dyn/∂ui - 10 次RRAM写入
   该因子类型每次写入: 70


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 35 次写入
2️⃣ 转置J^T: 15 次写入
3️⃣ 计算J^T * Ω: 15 次写入
4️⃣ 计算(J^T * Ω) * J: 25 次写入
5️⃣ 累加到H: 25 次写入

🎯 Hessian构建总结:
   每因子写入: 115
   因子数量: 13
   Hessian总写入: 1,495

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 29 次写入
2️⃣ 计算J^T * Ω * e: 5 次写入
3️⃣ 累加到g: 5 次写入

🎯 梯度构建总结:
   每因子写入: 39
   因子数量: 13
   梯度总写入: 507

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 20

2️⃣ 前向替换: L * y = -g
   前向替换写入: 12

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 12

🎯 系统求解总写入: 44

🎯 总RRAM写入统计:
   因子操作: 1,677
   Hessian额外: 1,040
   梯度额外: 130
   系统求解: 45
   总计: 2,892


🔍 完整分析: quadrotor - localization
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['IMU', 'Prior']
   因子总数: 21

📋 数学公式详解:
------------------------------------------------------------

🔹 IMU 因子:
   误差函数: e = [eω, ea, ep, ev, ebω, eba]
   详细公式: e = IMU_preintegration_error(xi, xi+1, vi, bi)
   变量定义: xi ∈ SE(3), xi+1 ∈ SE(3), vi ∈ R³, bi ∈ R⁶

   具体操作过程:
      Store: xi (pose) - 6 次RRAM写入
      Store: xi+1 (pose) - 6 次RRAM写入
      Store: vi (velocity) - 3 次RRAM写入
      Store: bi (bias) - 6 次RRAM写入
      Store: ωm (gyro) - 3 次RRAM写入
      Store: am (accel) - 3 次RRAM写入
      Exp: R_i = Exp(φ_i) - 9 次RRAM写入
      Exp: R_i+1 = Exp(φ_i+1) - 9 次RRAM写入
      Transpose: R_i^T - 9 次RRAM写入
      StaticVMM: R_i^T * R_i+1 - 9 次RRAM写入
      Log: Log(R_i^T * R_i+1) - 3 次RRAM写入
      VP: rotation error - 3 次RRAM写入
      VP: translation error - 3 次RRAM写入
      VP: velocity error - 3 次RRAM写入
      VP: bias error - 6 次RRAM写入
      Jacobian_IMU: ∂e/∂xi - 36 次RRAM写入
      Jacobian_IMU: ∂e/∂xi+1 - 36 次RRAM写入
      Jacobian_IMU: ∂e/∂vi - 18 次RRAM写入
      Jacobian_IMU: ∂e/∂bi - 36 次RRAM写入
   该因子类型每次写入: 207


🔹 Prior 因子:
   误差函数: e = x - x0
   详细公式: e = pose - prior_pose
   变量定义: x ∈ SE(3), x0 ∈ SE(3)

   具体操作过程:
      Store: x (pose) - 6 次RRAM写入
      Store: x0 (prior) - 6 次RRAM写入
      Exp: R = Exp(φ) - 9 次RRAM写入
      Exp: R0 = Exp(φ0) - 9 次RRAM写入
      Transpose: R0^T - 9 次RRAM写入
      StaticVMM: R0^T * R - 9 次RRAM写入
      Log: Log(R0^T * R) - 3 次RRAM写入
      VP: t - t0 - 3 次RRAM写入
      Jacobian_SE3: ∂e/∂x - 36 次RRAM写入
   该因子类型每次写入: 90


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 81 次写入
2️⃣ 转置J^T: 18 次写入
3️⃣ 计算J^T * Ω: 18 次写入
4️⃣ 计算(J^T * Ω) * J: 36 次写入
5️⃣ 累加到H: 36 次写入

🎯 Hessian构建总结:
   每因子写入: 189
   因子数量: 21
   Hessian总写入: 3,969

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 67 次写入
2️⃣ 计算J^T * Ω * e: 6 次写入
3️⃣ 累加到g: 6 次写入

🎯 梯度构建总结:
   每因子写入: 79
   因子数量: 21
   梯度总写入: 1,659

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 36

2️⃣ 前向替换: L * y = -g
   前向替换写入: 18

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 18

🎯 系统求解总写入: 72

🎯 总RRAM写入统计:
   因子操作: 6,237
   Hessian额外: 2,268
   梯度额外: 252
   系统求解: 72
   总计: 8,829


🔍 完整分析: quadrotor - planning
================================================================================
📊 算法信息:
   变量维度: 12
   因子类型: ['Smooth', 'Collision-free', 'Kinematics']
   因子总数: 17

📋 数学公式详解:
------------------------------------------------------------

🔹 Smooth 因子:
   误差函数: e = ||xi+1 - xi||²
   详细公式: e = (xi+1 - xi)^T * Q * (xi+1 - xi)
   变量定义: xi ∈ R¹², xi+1 ∈ R¹²

   具体操作过程:
      Store: xi (full state) - 12 次RRAM写入
      Store: xi+1 - 12 次RRAM写入
      VP: diff = xi+1 - xi - 12 次RRAM写入
      StaticVMM: Q * diff - 12 次RRAM写入
      VP: diff^T * (Q * diff) - 1 次RRAM写入
      Jacobian_Linear: ∂e/∂xi - 12 次RRAM写入
      Jacobian_Linear: ∂e/∂xi+1 - 12 次RRAM写入
   该因子类型每次写入: 73


🔹 Collision-free 因子:
   误差函数: e = max(0, threshold - distance(x, obstacles))
   详细公式: e = max(0, d_safe - min_i(||pos(x) - obs_i||))
   变量定义: x ∈ R¹², obstacles ∈ R^(n×3)

   具体操作过程:
      Store: x - 12 次RRAM写入
      Store: obstacles - 30 次RRAM写入
      Extract: position from state - 3 次RRAM写入
      DynamicVMM: distances - 10 次RRAM写入
      Min: min distance - 1 次RRAM写入
      VP: e = d_safe - d_min - 1 次RRAM写入
      Jacobian_Position: ∂pos/∂x - 36 次RRAM写入
      Jacobian_Distance: ∂d/∂pos - 3 次RRAM写入
   该因子类型每次写入: 96


🔹 Kinematics 因子:
   误差函数: e = xi+1 - f_kin(xi, ui)
   详细公式: e = xi+1 - quadrotor_kinematics(xi, ui)
   变量定义: xi ∈ R¹², ui ∈ R⁴, xi+1 ∈ R¹²

   具体操作过程:
      Store: xi (pos,vel,att,ω) - 12 次RRAM写入
      Store: ui (thrust,τ) - 4 次RRAM写入
      Store: xi+1 - 12 次RRAM写入
      Exp: R = Exp(φ) - 9 次RRAM写入
      DynamicVMM: quadrotor kinematics - 12 次RRAM写入
      VP: e = xi+1 - f_kin - 12 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂xi - 144 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂ui - 48 次RRAM写入
   该因子类型每次写入: 253


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 85 次写入
2️⃣ 转置J^T: 36 次写入
3️⃣ 计算J^T * Ω: 36 次写入
4️⃣ 计算(J^T * Ω) * J: 144 次写入
5️⃣ 累加到H: 144 次写入

🎯 Hessian构建总结:
   每因子写入: 445
   因子数量: 17
   Hessian总写入: 7,565

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 55 次写入
2️⃣ 计算J^T * Ω * e: 12 次写入
3️⃣ 累加到g: 12 次写入

🎯 梯度构建总结:
   每因子写入: 79
   因子数量: 17
   梯度总写入: 1,343

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 288

2️⃣ 前向替换: L * y = -g
   前向替换写入: 72

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 72

🎯 系统求解总写入: 432

🎯 总RRAM写入统计:
   因子操作: 7,174
   Hessian额外: 6,120
   梯度额外: 408
   系统求解: 432
   总计: 14,134


🔍 完整分析: quadrotor - control
================================================================================
📊 算法信息:
   变量维度: 状态12维, 控制5维
   因子类型: ['Kinematics', 'Dynamics']
   因子总数: 13

📋 数学公式详解:
------------------------------------------------------------

🔹 Kinematics 因子:
   误差函数: e = xi+1 - f_kin(xi, ui)
   详细公式: e = xi+1 - quadrotor_kinematics(xi, ui)
   变量定义: xi ∈ R¹², ui ∈ R⁵, xi+1 ∈ R¹²

   具体操作过程:
      Store: xi (pos,vel,att,ω) - 12 次RRAM写入
      Store: ui (f1,f2,f3,f4,τz) - 5 次RRAM写入
      Store: xi+1 - 12 次RRAM写入
      Exp: R = Exp(φ) - 9 次RRAM写入
      DynamicVMM: thrust allocation - 12 次RRAM写入
      DynamicVMM: quadrotor kinematics - 12 次RRAM写入
      VP: e = xi+1 - f_kin - 12 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂xi - 144 次RRAM写入
      Jacobian_Kinematics: ∂f_kin/∂ui - 60 次RRAM写入
   该因子类型每次写入: 278


🔹 Dynamics 因子:
   误差函数: e = xi+1 - f_dyn(xi, ui)
   详细公式: e = xi+1 - quadrotor_dynamics(xi, ui)
   变量定义: xi ∈ R¹², ui ∈ R⁵, xi+1 ∈ R¹²

   具体操作过程:
      Store: xi - 12 次RRAM写入
      Store: ui - 5 次RRAM写入
      Store: xi+1 - 12 次RRAM写入
      Exp: R = Exp(φ) - 9 次RRAM写入
      DynamicVMM: aerodynamic forces - 15 次RRAM写入
      DynamicVMM: quadrotor dynamics - 24 次RRAM写入
      VP: e = xi+1 - f_dyn - 12 次RRAM写入
      Jacobian_Dynamics: ∂f_dyn/∂xi - 144 次RRAM写入
      Jacobian_Dynamics: ∂f_dyn/∂ui - 60 次RRAM写入
   该因子类型每次写入: 293


📋 Hessian矩阵构建: H = J^T * Ω * J
------------------------------------------------------------
每个因子的Hessian构建:
1️⃣ 计算Jacobian: 平均 204 次写入
2️⃣ 转置J^T: 36 次写入
3️⃣ 计算J^T * Ω: 36 次写入
4️⃣ 计算(J^T * Ω) * J: 144 次写入
5️⃣ 累加到H: 144 次写入

🎯 Hessian构建总结:
   每因子写入: 564
   因子数量: 13
   Hessian总写入: 7,332

📋 梯度向量构建: g = J^T * Ω * e
------------------------------------------------------------
每个因子的梯度构建:
1️⃣ 计算误差e: 平均 81 次写入
2️⃣ 计算J^T * Ω * e: 12 次写入
3️⃣ 累加到g: 12 次写入

🎯 梯度构建总结:
   每因子写入: 105
   因子数量: 13
   梯度总写入: 1,365

📋 线性系统求解: H * Δx = -g
------------------------------------------------------------
1️⃣ Cholesky分解: H = L * L^T
   Cholesky分解写入: 288

2️⃣ 前向替换: L * y = -g
   前向替换写入: 72

3️⃣ 后向替换: L^T * Δx = y
   后向替换写入: 72

🎯 系统求解总写入: 432

🎯 总RRAM写入统计:
   因子操作: 7,423
   Hessian额外: 4,680
   梯度额外: 312
   系统求解: 432
   总计: 12,847

🎯 总结
================================================================================
12个算法总RRAM写入: 161,495
平均每算法: 13,457

关键发现:
1. 每个算法都有特定的数学公式和因子类型
2. EXP/LOG mapping在所有位姿相关算法中都必需
3. Transpose操作在矩阵计算中大量使用
4. 线性系统求解的复杂度随变量维度立方增长
5. 控制算法比定位算法需要更多的动力学计算
