[{"app_name": "robot", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 70, "total_factors": 212, "factor_types": ["meas86", "meas97", "meas71", "meas104", "meas129", "meas94", "meas149", "meas74", "meas20", "meas27", "meas151", "meas98", "meas9", "odom33", "meas117", "meas145", "meas4", "meas81", "meas147", "meas33", "meas140", "meas13", "meas139", "odom19", "meas44", "meas50", "meas134", "meas161", "meas25", "meas39", "meas55", "meas77", "odom38", "meas103", "meas64", "meas90", "meas153", "meas89", "meas60", "meas114", "odom45", "meas65", "meas5", "meas100", "meas132", "meas148", "odom36", "meas35", "meas105", "meas92", "meas142", "meas28", "meas141", "meas48", "meas3", "meas18", "odom40", "meas31", "meas29", "meas88", "odom9", "meas38", "odom24", "meas143", "meas49", "odom28", "meas84", "meas137", "odom41", "meas138", "meas32", "meas91", "odom27", "meas57", "meas109", "odom48", "meas0", "meas160", "odom37", "meas52", "odom29", "odom32", "odom8", "meas36", "meas82", "odom2", "meas42", "meas70", "odom11", "meas37", "odom21", "odom42", "meas2", "meas76", "meas150", "odom0", "meas24", "meas133", "odom16", "meas1", "meas110", "meas135", "odom6", "meas111", "odom34", "meas108", "meas54", "meas102", "meas67", "meas80", "meas99", "meas53", "meas34", "meas11", "meas17", "meas128", "odom5", "odom1", "odom17", "meas144", "meas87", "meas8", "meas113", "meas122", "odom31", "meas106", "meas158", "meas75", "meas159", "odom18", "meas96", "meas156", "meas46", "meas47", "meas56", "odom20", "meas152", "meas45", "meas10", "meas22", "meas155", "meas118", "meas112", "odom10", "meas68", "meas51", "meas115", "meas107", "meas63", "meas15", "odom26", "meas121", "meas131", "meas119", "odom46", "odom25", "odom39", "odom43", "meas116", "meas146", "meas7", "meas79", "meas12", "meas43", "meas16", "meas23", "meas21", "meas154", "odom30", "odom7", "meas127", "meas41", "odom4", "meas123", "meas19", "meas83", "meas85", "meas120", "meas124", "odom13", "odom47", "meas157", "meas95", "meas69", "prior", "odom3", "meas125", "odom23", "odom22", "meas59", "meas78", "meas40", "meas61", "odom35", "meas30", "meas93", "meas136", "meas73", "odom14", "meas6", "meas58", "meas66", "meas126", "odom44", "meas72", "odom15", "meas101", "odom12", "meas62", "meas130", "meas26", "meas14"], "error_formula_type": "pose_landmark_observation", "computation_complexity": "high", "store_operations": 4240, "acim_operations": 42400, "weight_setup_once": 63, "simd_operations": 6360, "writes_per_iteration": 46640, "estimated_iterations": 12, "total_writes": 559743}, {"app_name": "robot", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 30, "total_factors": 52, "factor_types": ["collision20", "smooth3", "smooth22", "collision0", "smooth23", "smooth21", "smooth7", "collision12", "collision6", "collision7", "goal", "collision8", "collision4", "smooth27", "collision3", "smooth2", "smooth12", "smooth17", "collision5", "smooth10", "smooth1", "start", "smooth19", "smooth18", "collision11", "smooth13", "collision10", "smooth25", "smooth5", "collision1", "collision14", "collision15", "smooth6", "smooth4", "smooth14", "smooth16", "smooth9", "smooth8", "collision18", "collision16", "smooth20", "smooth26", "smooth0", "smooth15", "collision2", "collision13", "smooth28", "collision9", "collision17", "smooth11", "collision19", "smooth24"], "error_formula_type": "path_smoothness_collision", "computation_complexity": "medium", "store_operations": 1040, "acim_operations": 6500, "weight_setup_once": 63, "simd_operations": 1560, "writes_per_iteration": 7540, "estimated_iterations": 15, "total_writes": 113163}, {"app_name": "robot", "algorithm": "control", "algorithm_type": "control", "total_variables": 39, "total_factors": 61, "factor_types": ["dyn18", "dyn11", "cost10", "cost16", "cost4", "dyn16", "state", "dyn17", "input", "dyn8", "cost7", "cost3", "dyn7", "cost0", "dyn0", "cost6", "dyn1", "cost15", "dyn3", "dyn9", "cost9", "cost5", "dyn10", "final", "dyn14", "cost13", "cost1", "dyn6", "dyn15", "cost14", "dyn5", "dyn4", "cost18", "dyn12", "cost12", "dyn13", "cost11", "dyn2", "cost17", "cost8", "cost2"], "error_formula_type": "nonlinear_mpc", "computation_complexity": "high", "store_operations": 1220, "acim_operations": 12200, "weight_setup_once": 63, "simd_operations": 1830, "writes_per_iteration": 13420, "estimated_iterations": 8, "total_writes": 107423}, {"app_name": "manipulator", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 1, "total_factors": 1, "factor_types": ["prior"], "error_formula_type": "joint_state_estimation", "computation_complexity": "low", "store_operations": 20, "acim_operations": 50, "weight_setup_once": 63, "simd_operations": 30, "writes_per_iteration": 70, "estimated_iterations": 10, "total_writes": 763}, {"app_name": "manipulator", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 25, "total_factors": 59, "factor_types": ["smooth3", "smooth22", "smooth23", "smooth21", "workspace13", "smooth7", "collision6", "joint", "goal", "smooth2", "smooth12", "smooth17", "smooth10", "smooth1", "start", "smooth19", "smooth18", "smooth13", "collision10", "smooth5", "collision14", "workspace21", "smooth6", "smooth4", "smooth14", "smooth16", "smooth9", "smooth8", "collision18", "smooth20", "smooth0", "smooth15", "collision2", "workspace5", "smooth11"], "error_formula_type": "joint_trajectory_planning", "computation_complexity": "medium", "store_operations": 1180, "acim_operations": 7375, "weight_setup_once": 63, "simd_operations": 1770, "writes_per_iteration": 8555, "estimated_iterations": 20, "total_writes": 171163}, {"app_name": "manipulator", "algorithm": "control", "algorithm_type": "control", "total_variables": 39, "total_factors": 61, "factor_types": ["dyn18", "dyn11", "cost10", "cost16", "cost4", "dyn16", "state", "dyn17", "input", "dyn8", "cost7", "cost3", "dyn7", "cost0", "dyn0", "cost6", "dyn1", "cost15", "dyn3", "dyn9", "cost9", "cost5", "dyn10", "final", "dyn14", "cost13", "cost1", "dyn6", "dyn15", "cost14", "dyn5", "dyn4", "cost18", "dyn12", "cost12", "dyn13", "cost11", "dyn2", "cost17", "cost8", "cost2"], "error_formula_type": "joint_space_control", "computation_complexity": "medium", "store_operations": 1220, "acim_operations": 7625, "weight_setup_once": 63, "simd_operations": 1830, "writes_per_iteration": 8845, "estimated_iterations": 12, "total_writes": 106203}, {"app_name": "autovehicle", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 60, "total_factors": 251, "factor_types": ["meas86", "meas97", "meas71", "meas104", "meas129", "meas94", "meas197", "meas149", "meas74", "meas20", "meas27", "meas151", "meas98", "meas9", "odom33", "meas117", "meas145", "meas4", "meas81", "meas147", "meas33", "meas140", "meas13", "meas165", "meas139", "odom19", "meas208", "meas44", "meas195", "meas180", "meas50", "meas203", "meas207", "meas167", "meas134", "meas161", "meas172", "meas25", "meas39", "meas55", "meas77", "meas190", "odom38", "meas103", "meas64", "meas179", "meas192", "meas90", "meas153", "meas186", "meas89", "meas60", "meas114", "meas65", "meas5", "meas100", "meas132", "meas148", "odom36", "meas209", "meas35", "meas105", "meas92", "meas142", "meas28", "meas141", "meas48", "meas168", "meas3", "meas18", "meas31", "meas169", "meas29", "meas88", "meas210", "odom9", "meas38", "odom24", "meas199", "meas143", "meas49", "odom28", "meas84", "meas137", "meas138", "meas32", "meas91", "meas181", "odom27", "meas57", "meas109", "meas0", "meas160", "odom37", "meas52", "meas174", "odom29", "odom32", "odom8", "meas36", "meas82", "odom2", "meas42", "meas70", "odom11", "meas37", "meas187", "meas173", "odom21", "meas2", "meas76", "meas150", "meas196", "meas175", "odom0", "meas24", "meas133", "odom16", "meas1", "meas110", "meas135", "odom6", "meas111", "odom34", "meas108", "meas54", "meas102", "meas67", "meas80", "meas99", "meas53", "meas34", "meas11", "meas17", "meas200", "meas128", "odom5", "odom1", "odom17", "meas144", "meas87", "meas8", "meas113", "meas122", "odom31", "meas198", "meas106", "meas158", "meas166", "meas75", "meas188", "meas159", "odom18", "meas96", "meas156", "meas46", "meas47", "meas56", "odom20", "meas152", "meas45", "meas205", "meas10", "meas22", "meas155", "meas118", "meas112", "odom10", "meas68", "meas51", "meas115", "meas191", "meas107", "meas63", "meas15", "odom26", "meas121", "meas131", "meas162", "meas119", "meas201", "odom25", "meas116", "meas146", "meas7", "meas79", "meas12", "meas43", "meas16", "meas23", "meas182", "meas21", "meas163", "meas154", "odom30", "odom7", "meas127", "meas41", "odom4", "meas123", "meas19", "meas83", "meas85", "meas120", "meas124", "meas164", "odom13", "meas157", "meas170", "meas95", "meas69", "meas189", "meas193", "prior", "odom3", "meas125", "odom23", "meas204", "odom22", "meas183", "meas185", "meas59", "meas78", "meas40", "meas61", "odom35", "meas30", "meas93", "meas136", "meas73", "odom14", "meas6", "meas184", "meas58", "meas66", "meas126", "meas72", "odom15", "meas202", "meas101", "odom12", "meas171", "meas194", "meas177", "meas62", "meas206", "meas130", "meas26", "meas14", "meas176", "meas178"], "error_formula_type": "dense_sensor_fusion", "computation_complexity": "very_high", "store_operations": 5020, "acim_operations": 75300, "weight_setup_once": 63, "simd_operations": 7530, "writes_per_iteration": 80320, "estimated_iterations": 15, "total_writes": 1204863}, {"app_name": "autovehicle", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 8, "total_factors": 17, "factor_types": ["coll1", "kinematics0", "kinematics6", "kinematics3", "start", "kinematics5", "coll5", "goal", "kinematics1", "kinematics4", "coll4", "coll2", "coll0", "coll7", "kinematics2", "coll6", "coll3"], "error_formula_type": "kinematic_constraints", "computation_complexity": "medium", "store_operations": 340, "acim_operations": 2125, "weight_setup_once": 63, "simd_operations": 510, "writes_per_iteration": 2465, "estimated_iterations": 25, "total_writes": 61688}, {"app_name": "autovehicle", "algorithm": "control", "algorithm_type": "control", "total_variables": 9, "total_factors": 13, "factor_types": ["final", "kin0", "cost0", "kin2", "cost1", "dyn2", "dyn0", "kin1", "dyn1", "dyn3", "kin3", "cost3", "cost2"], "error_formula_type": "vehicle_dynamics_control", "computation_complexity": "medium", "store_operations": 260, "acim_operations": 1625, "weight_setup_once": 63, "simd_operations": 390, "writes_per_iteration": 1885, "estimated_iterations": 10, "total_writes": 18913}, {"app_name": "quadrotor", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 15, "total_factors": 21, "factor_types": ["imu8", "imu2", "cam3", "cam4", "cam6", "imu7", "cam7", "cam8", "cam9", "imu1", "imu3", "imu0", "prior", "imu4", "imu6", "cam0", "cam1", "imu5", "cam10", "cam5", "cam2"], "error_formula_type": "visual_inertial_odometry", "computation_complexity": "high", "store_operations": 420, "acim_operations": 4200, "weight_setup_once": 63, "simd_operations": 630, "writes_per_iteration": 4620, "estimated_iterations": 18, "total_writes": 83223}, {"app_name": "quadrotor", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 8, "total_factors": 17, "factor_types": ["coll1", "kinematics0", "kinematics6", "kinematics3", "start", "kinematics5", "coll5", "goal", "kinematics1", "kinematics4", "coll4", "coll2", "coll0", "coll7", "kinematics2", "coll6", "coll3"], "error_formula_type": "trajectory_optimization", "computation_complexity": "medium", "store_operations": 340, "acim_operations": 2125, "weight_setup_once": 63, "simd_operations": 510, "writes_per_iteration": 2465, "estimated_iterations": 30, "total_writes": 74013}, {"app_name": "quadrotor", "algorithm": "control", "algorithm_type": "control", "total_variables": 9, "total_factors": 13, "factor_types": ["final", "kin0", "cost0", "kin2", "cost1", "dyn2", "dyn0", "kin1", "dyn1", "dyn3", "kin3", "cost3", "cost2"], "error_formula_type": "quadrotor_dynamics_control", "computation_complexity": "high", "store_operations": 260, "acim_operations": 2600, "weight_setup_once": 63, "simd_operations": 390, "writes_per_iteration": 2860, "estimated_iterations": 15, "total_writes": 42963}]