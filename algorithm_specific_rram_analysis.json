[{"app_name": "robot", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 70, "total_factors": 212, "factor_types": ["odom36", "meas92", "odom47", "meas91", "meas43", "meas49", "meas123", "meas116", "meas25", "meas108", "odom6", "meas112", "meas27", "meas154", "meas69", "odom32", "meas79", "meas152", "meas71", "meas78", "meas30", "odom20", "odom26", "meas121", "odom16", "meas126", "meas54", "odom21", "meas17", "meas73", "odom24", "odom41", "meas85", "odom34", "odom5", "meas5", "meas31", "meas147", "odom4", "meas18", "meas59", "odom7", "meas135", "meas161", "meas36", "meas40", "meas137", "meas19", "meas153", "odom29", "meas93", "meas46", "meas95", "odom40", "odom0", "meas48", "meas60", "meas64", "meas72", "meas99", "meas100", "meas102", "meas151", "meas118", "meas138", "meas29", "meas144", "meas28", "odom14", "meas105", "odom43", "meas8", "meas84", "meas117", "meas10", "meas119", "meas134", "meas113", "meas7", "odom3", "odom46", "meas0", "meas143", "meas38", "meas130", "meas13", "meas24", "meas1", "meas65", "meas11", "odom33", "meas76", "meas157", "odom9", "meas139", "meas22", "meas66", "meas50", "meas109", "meas132", "meas114", "meas107", "meas15", "meas83", "meas90", "odom11", "meas32", "meas63", "odom15", "meas145", "meas44", "meas131", "meas41", "meas149", "odom13", "meas62", "meas96", "meas98", "odom48", "meas45", "meas111", "odom44", "meas81", "odom28", "odom17", "meas68", "meas128", "meas97", "meas148", "meas55", "odom12", "meas80", "meas127", "odom19", "meas140", "odom27", "meas159", "odom22", "meas75", "meas9", "odom42", "meas26", "meas14", "meas47", "meas86", "meas4", "meas89", "meas82", "odom30", "meas77", "meas158", "odom31", "odom35", "odom37", "odom39", "meas39", "meas74", "meas23", "meas56", "odom38", "meas61", "odom18", "meas136", "meas129", "prior", "meas12", "meas110", "meas21", "odom10", "meas101", "meas115", "meas3", "odom8", "odom25", "meas87", "meas58", "meas52", "meas160", "meas104", "meas155", "odom1", "odom23", "meas142", "meas70", "meas103", "meas42", "meas150", "meas16", "meas67", "meas35", "meas94", "meas125", "odom45", "meas133", "meas88", "meas141", "meas34", "meas57", "meas51", "meas120", "meas156", "meas33", "meas20", "meas146", "meas124", "meas106", "meas37", "meas53", "meas2", "meas122", "odom2", "meas6"], "error_formula_type": "pose_landmark_observation", "computation_complexity": "high", "store_operations": 2120, "acim_operations": 8600, "weight_setup_once": 24, "simd_operations": 3180, "writes_per_iteration": 10720, "estimated_iterations": 12, "total_writes": 128664}, {"app_name": "robot", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 30, "total_factors": 52, "factor_types": ["collision14", "start", "smooth16", "collision4", "smooth2", "collision8", "collision0", "smooth5", "smooth25", "collision20", "smooth1", "collision11", "collision13", "collision3", "smooth27", "collision2", "smooth26", "smooth23", "collision12", "collision1", "collision7", "collision9", "smooth9", "smooth28", "smooth6", "smooth19", "goal", "smooth8", "smooth11", "smooth22", "smooth3", "collision17", "smooth0", "smooth7", "collision19", "collision18", "smooth15", "smooth21", "collision10", "collision15", "smooth18", "smooth4", "smooth20", "smooth13", "collision16", "smooth14", "collision5", "collision6", "smooth10", "smooth24", "smooth12", "smooth17"], "error_formula_type": "path_smoothness_collision", "computation_complexity": "medium", "store_operations": 312, "acim_operations": 1200, "weight_setup_once": 29, "simd_operations": 468, "writes_per_iteration": 1512, "estimated_iterations": 15, "total_writes": 22709}, {"app_name": "robot", "algorithm": "control", "algorithm_type": "control", "total_variables": 39, "total_factors": 61, "factor_types": ["cost10", "cost9", "dyn0", "dyn12", "dyn2", "dyn5", "cost0", "cost8", "dyn13", "cost16", "cost2", "state", "dyn15", "cost5", "dyn18", "dyn7", "cost12", "dyn3", "cost7", "dyn4", "final", "dyn16", "cost3", "input", "cost6", "dyn1", "cost14", "cost17", "dyn8", "dyn10", "cost1", "dyn9", "cost11", "cost15", "cost13", "dyn17", "cost18", "cost4", "dyn11", "dyn14", "dyn6"], "error_formula_type": "nonlinear_mpc", "computation_complexity": "high", "store_operations": 610, "acim_operations": 6220, "weight_setup_once": 45, "simd_operations": 915, "writes_per_iteration": 6830, "estimated_iterations": 8, "total_writes": 54685}, {"app_name": "manipulator", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 1, "total_factors": 1, "factor_types": ["prior"], "error_formula_type": "joint_state_estimation", "computation_complexity": "low", "store_operations": 2, "acim_operations": 3, "weight_setup_once": 21, "simd_operations": 3, "writes_per_iteration": 5, "estimated_iterations": 10, "total_writes": 71}, {"app_name": "manipulator", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 25, "total_factors": 59, "factor_types": ["collision14", "start", "workspace13", "smooth16", "smooth2", "smooth5", "smooth1", "workspace5", "collision2", "smooth23", "smooth9", "smooth6", "smooth19", "goal", "smooth8", "smooth11", "smooth22", "smooth3", "smooth0", "smooth7", "workspace21", "collision18", "smooth15", "smooth21", "collision10", "joint", "smooth18", "smooth4", "smooth20", "smooth13", "smooth14", "collision6", "smooth10", "smooth12", "smooth17"], "error_formula_type": "joint_trajectory_planning", "computation_complexity": "medium", "store_operations": 708, "acim_operations": 5415, "weight_setup_once": 56, "simd_operations": 1062, "writes_per_iteration": 6123, "estimated_iterations": 20, "total_writes": 122516}, {"app_name": "manipulator", "algorithm": "control", "algorithm_type": "control", "total_variables": 39, "total_factors": 61, "factor_types": ["cost10", "cost9", "dyn0", "dyn12", "dyn2", "dyn5", "cost0", "cost8", "dyn13", "cost16", "cost2", "state", "dyn15", "cost5", "dyn18", "dyn7", "cost12", "dyn3", "cost7", "dyn4", "final", "dyn16", "cost3", "input", "cost6", "dyn1", "cost14", "cost17", "dyn8", "dyn10", "cost1", "dyn9", "cost11", "cost15", "cost13", "dyn17", "cost18", "cost4", "dyn11", "dyn14", "dyn6"], "error_formula_type": "joint_space_control", "computation_complexity": "medium", "store_operations": 1464, "acim_operations": 11370, "weight_setup_once": 56, "simd_operations": 2196, "writes_per_iteration": 12834, "estimated_iterations": 12, "total_writes": 154064}, {"app_name": "autovehicle", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 60, "total_factors": 251, "factor_types": ["odom36", "meas197", "meas92", "meas91", "meas43", "meas49", "meas123", "meas116", "meas25", "meas108", "odom6", "meas112", "meas27", "meas154", "meas69", "meas186", "meas194", "meas206", "odom32", "meas79", "meas191", "meas176", "meas152", "meas71", "meas78", "meas30", "odom20", "odom26", "meas121", "odom16", "meas126", "meas54", "odom21", "meas17", "meas73", "odom24", "meas85", "odom34", "odom5", "meas5", "meas31", "meas147", "meas170", "meas207", "odom4", "meas18", "meas59", "odom7", "meas135", "meas161", "meas36", "meas40", "meas137", "meas19", "meas153", "odom29", "meas93", "meas46", "meas174", "meas95", "meas100", "odom0", "meas48", "meas60", "meas64", "meas72", "meas99", "meas102", "meas151", "meas118", "meas138", "meas29", "meas144", "meas210", "meas28", "odom14", "meas105", "meas8", "meas84", "meas117", "meas10", "meas119", "meas134", "meas199", "meas113", "meas7", "odom3", "meas180", "meas0", "meas143", "meas38", "meas130", "meas172", "meas13", "meas24", "meas1", "meas166", "meas65", "meas196", "meas11", "meas163", "odom33", "meas76", "meas157", "odom9", "meas139", "meas22", "meas184", "meas66", "meas50", "meas109", "meas132", "meas114", "meas107", "meas15", "meas182", "meas83", "meas90", "odom11", "meas32", "meas63", "odom15", "meas165", "meas145", "meas44", "meas204", "meas208", "meas131", "meas41", "meas149", "meas164", "odom13", "meas62", "meas96", "meas98", "meas187", "meas45", "meas111", "meas81", "odom28", "odom17", "meas68", "meas128", "meas97", "meas148", "meas55", "meas203", "odom12", "meas80", "meas127", "odom19", "meas140", "odom27", "meas159", "odom22", "meas75", "meas9", "meas86", "meas26", "meas14", "meas47", "meas189", "meas4", "meas89", "meas82", "meas168", "odom30", "meas177", "meas188", "meas77", "meas158", "odom31", "odom35", "odom37", "meas167", "meas178", "meas179", "meas39", "meas74", "meas162", "meas200", "meas23", "meas56", "meas201", "meas205", "odom38", "meas61", "odom18", "meas136", "meas129", "prior", "meas12", "meas110", "meas21", "odom10", "meas193", "meas101", "meas115", "meas198", "meas173", "meas3", "odom8", "meas195", "odom25", "meas87", "meas58", "meas52", "meas160", "meas104", "meas155", "odom1", "odom23", "meas142", "meas70", "meas103", "meas42", "meas175", "meas150", "meas16", "meas67", "meas35", "meas94", "meas125", "meas141", "meas133", "meas88", "meas34", "meas185", "meas57", "meas51", "meas190", "meas120", "meas181", "meas192", "meas169", "meas156", "meas202", "meas171", "meas33", "meas20", "meas146", "meas124", "meas106", "meas183", "meas37", "meas53", "meas2", "meas209", "meas122", "odom2", "meas6"], "error_formula_type": "dense_sensor_fusion", "computation_complexity": "very_high", "store_operations": 7530, "acim_operations": 136980, "weight_setup_once": 56, "simd_operations": 11295, "writes_per_iteration": 144510, "estimated_iterations": 15, "total_writes": 2167706}, {"app_name": "autovehicle", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 8, "total_factors": 17, "factor_types": ["kinematics0", "start", "kinematics3", "kinematics5", "coll2", "kinematics1", "kinematics6", "coll4", "coll5", "coll3", "kinematics4", "coll6", "coll0", "goal", "coll1", "kinematics2", "coll7"], "error_formula_type": "kinematic_constraints", "computation_complexity": "medium", "store_operations": 136, "acim_operations": 730, "weight_setup_once": 36, "simd_operations": 204, "writes_per_iteration": 866, "estimated_iterations": 25, "total_writes": 21686}, {"app_name": "autovehicle", "algorithm": "control", "algorithm_type": "control", "total_variables": 9, "total_factors": 13, "factor_types": ["kin2", "dyn1", "dyn0", "dyn2", "final", "kin1", "cost0", "dyn3", "kin3", "cost3", "cost1", "cost2", "kin0"], "error_formula_type": "vehicle_dynamics_control", "computation_complexity": "medium", "store_operations": 156, "acim_operations": 885, "weight_setup_once": 36, "simd_operations": 234, "writes_per_iteration": 1041, "estimated_iterations": 10, "total_writes": 10446}, {"app_name": "quadrotor", "algorithm": "localization", "algorithm_type": "localization", "total_variables": 15, "total_factors": 21, "factor_types": ["prior", "imu8", "imu0", "imu5", "cam7", "imu2", "cam3", "imu4", "imu6", "cam8", "cam9", "imu3", "imu7", "imu1", "cam0", "cam4", "cam5", "cam6", "cam2", "cam10", "cam1"], "error_formula_type": "visual_inertial_odometry", "computation_complexity": "high", "store_operations": 630, "acim_operations": 12300, "weight_setup_once": 101, "simd_operations": 945, "writes_per_iteration": 12930, "estimated_iterations": 18, "total_writes": 232841}, {"app_name": "quadrotor", "algorithm": "planning", "algorithm_type": "planning", "total_variables": 8, "total_factors": 17, "factor_types": ["kinematics0", "start", "kinematics3", "kinematics5", "coll2", "kinematics1", "kinematics6", "coll4", "coll5", "coll3", "kinematics4", "coll6", "coll0", "goal", "coll1", "kinematics2", "coll7"], "error_formula_type": "trajectory_optimization", "computation_complexity": "medium", "store_operations": 340, "acim_operations": 2825, "weight_setup_once": 56, "simd_operations": 510, "writes_per_iteration": 3165, "estimated_iterations": 30, "total_writes": 95006}, {"app_name": "quadrotor", "algorithm": "control", "algorithm_type": "control", "total_variables": 9, "total_factors": 13, "factor_types": ["kin2", "dyn1", "dyn0", "dyn2", "final", "kin1", "cost0", "dyn3", "kin3", "cost3", "cost1", "cost2", "kin0"], "error_formula_type": "quadrotor_dynamics_control", "computation_complexity": "high", "store_operations": 416, "acim_operations": 11072, "weight_setup_once": 164, "simd_operations": 624, "writes_per_iteration": 11488, "estimated_iterations": 15, "total_writes": 172484}]