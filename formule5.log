🚀 修正的线性方程系统构建过程分析
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解:
1. 误差计算 → 右侧向量 b (前向遍历)
2. Hessian构建 → 左侧矩阵 H (反向遍历)
3. 统一位姿表示的正确公式
4. 原语操作的准确使用


🔍 修正分析: robot - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 212
   误差计算总写入: 9,540

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于Figure 11的反向遍历过程:

🔹 反向遍历因子图构建Hessian:
   1️⃣ 反向遍历因子图 (从输出到输入)
   2️⃣ 对每个因子计算Jacobian ∂eᵢ/∂x
   3️⃣ 计算Jacobian转置 Jᵢᵀ
   4️⃣ 信息矩阵加权 Jᵢᵀ * Ωᵢ
   5️⃣ Hessian块计算 (Jᵢᵀ * Ωᵢ) * Jᵢ
   6️⃣ 累加到全局Hessian H += Jᵢᵀ * Ωᵢ * Jᵢ

   原语操作详解:
      Jr: Right Jacobian computation - 9 次写入
      Jr_inv: Right Jacobian inverse - 9 次写入
      RT: Jacobian transpose - 0 次写入
      RR: Jacobian chain rule - 9 次写入
      Hessian块: (Jᵢᵀ * Ωᵢ) * Jᵢ - 18 次写入
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 45
   因子数量: 212
   Hessian总写入: 9,540

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 424 次 × 9 = 3816 写入
      Log: 212 次 × 3 = 636 写入
      RT: 636 次 × 0 = 0 写入
      RR: 424 次 × 9 = 3816 写入
      RV: 424 次 × 3 = 1272 写入
      VP: 636 次 × 0 = 0 写入
      Jr: 424 次 × 9 = 3816 写入

🎯 原语操作总写入: 13,356

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 14,840
   Hessian构建 (左侧H): 13,992
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 28,862


🔍 修正分析: robot - planning
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 规划算法误差计算 (使用⊕操作):
   世界坐标计算: 使用⊕计算机器人链接点的世界坐标

   原语操作序列:
      VP: 路径点差值计算 - 0 次写入 (SIMD不需要写入)
      RR: 旋转组合 - 9 次写入
      VP: 平滑性误差 - 0 次写入 (SIMD不需要写入)
      Distance: 碰撞检测距离 - 10 次写入
      Min: 最小距离 - 0 次写入 (SIMD不需要写入)

🎯 误差计算总结:
   每因子写入: 19
   因子数量: 52
   误差计算总写入: 988

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于Figure 11的反向遍历过程:

🔹 反向遍历因子图构建Hessian:
   1️⃣ 反向遍历因子图 (从输出到输入)
   2️⃣ 对每个因子计算Jacobian ∂eᵢ/∂x
   3️⃣ 计算Jacobian转置 Jᵢᵀ
   4️⃣ 信息矩阵加权 Jᵢᵀ * Ωᵢ
   5️⃣ Hessian块计算 (Jᵢᵀ * Ωᵢ) * Jᵢ
   6️⃣ 累加到全局Hessian H += Jᵢᵀ * Ωᵢ * Jᵢ

   原语操作详解:
      Jr: Right Jacobian computation - 9 次写入
      Jr_inv: Right Jacobian inverse - 9 次写入
      RT: Jacobian transpose - 0 次写入
      RR: Jacobian chain rule - 36 次写入
      Hessian块: (Jᵢᵀ * Ωᵢ) * Jᵢ - 72 次写入
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 126
   因子数量: 52
   Hessian总写入: 6,552

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      VP: 260 次 × 0 = 0 写入
      RR: 52 次 × 9 = 468 写入
      Distance: 520 次 (自定义操作)

🎯 原语操作总写入: 468

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 1,560
   Hessian构建 (左侧H): 9,048
   QR分解求解:
     - 预处理: 36
     - 分解: 72
     - 求解: 6
   系统求解总计: 114
   总计: 10,722


🔍 修正分析: robot - control
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 控制算法误差计算 (使用Θ操作):
   偏差计算: 实际观测与理想模型的偏差

   原语操作序列:
      VP: 状态预测误差 - 0 次写入 (SIMD不需要写入)
      RR: 动力学模型计算 - 9 次写入
      VP: 控制误差 - 0 次写入 (SIMD不需要写入)

🎯 误差计算总结:
   每因子写入: 9
   因子数量: 61
   误差计算总写入: 549

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于Figure 11的反向遍历过程:

🔹 反向遍历因子图构建Hessian:
   1️⃣ 反向遍历因子图 (从输出到输入)
   2️⃣ 对每个因子计算Jacobian ∂eᵢ/∂x
   3️⃣ 计算Jacobian转置 Jᵢᵀ
   4️⃣ 信息矩阵加权 Jᵢᵀ * Ωᵢ
   5️⃣ Hessian块计算 (Jᵢᵀ * Ωᵢ) * Jᵢ
   6️⃣ 累加到全局Hessian H += Jᵢᵀ * Ωᵢ * Jᵢ

   原语操作详解:
      Jr: Right Jacobian computation - 9 次写入
      Jr_inv: Right Jacobian inverse - 9 次写入
      RT: Jacobian transpose - 0 次写入
      RR: Jacobian chain rule - 9 次写入
      Hessian块: (Jᵢᵀ * Ωᵢ) * Jᵢ - 18 次写入
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 45
   因子数量: 61
   Hessian总写入: 2,745

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      VP: 244 次 × 0 = 0 写入
      RR: 61 次 × 9 = 549 写入
      Dynamics: 305 次 (自定义操作)

🎯 原语操作总写入: 549

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 1,220
   Hessian构建 (左侧H): 4,026
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 5,276


🔍 修正分析: autovehicle - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 251
   误差计算总写入: 11,295

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于Figure 11的反向遍历过程:

🔹 反向遍历因子图构建Hessian:
   1️⃣ 反向遍历因子图 (从输出到输入)
   2️⃣ 对每个因子计算Jacobian ∂eᵢ/∂x
   3️⃣ 计算Jacobian转置 Jᵢᵀ
   4️⃣ 信息矩阵加权 Jᵢᵀ * Ωᵢ
   5️⃣ Hessian块计算 (Jᵢᵀ * Ωᵢ) * Jᵢ
   6️⃣ 累加到全局Hessian H += Jᵢᵀ * Ωᵢ * Jᵢ

   原语操作详解:
      Jr: Right Jacobian computation - 9 次写入
      Jr_inv: Right Jacobian inverse - 9 次写入
      RT: Jacobian transpose - 0 次写入
      RR: Jacobian chain rule - 9 次写入
      Hessian块: (Jᵢᵀ * Ωᵢ) * Jᵢ - 18 次写入
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 45
   因子数量: 251
   Hessian总写入: 11,295

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 502 次 × 9 = 4518 写入
      Log: 251 次 × 3 = 753 写入
      RT: 753 次 × 0 = 0 写入
      RR: 502 次 × 9 = 4518 写入
      RV: 502 次 × 3 = 1506 写入
      VP: 753 次 × 0 = 0 写入
      Jr: 502 次 × 9 = 4518 写入

🎯 原语操作总写入: 15,813

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 17,570
   Hessian构建 (左侧H): 16,566
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 34,166


🔍 修正分析: quadrotor - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 21
   误差计算总写入: 945

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于Figure 11的反向遍历过程:

🔹 反向遍历因子图构建Hessian:
   1️⃣ 反向遍历因子图 (从输出到输入)
   2️⃣ 对每个因子计算Jacobian ∂eᵢ/∂x
   3️⃣ 计算Jacobian转置 Jᵢᵀ
   4️⃣ 信息矩阵加权 Jᵢᵀ * Ωᵢ
   5️⃣ Hessian块计算 (Jᵢᵀ * Ωᵢ) * Jᵢ
   6️⃣ 累加到全局Hessian H += Jᵢᵀ * Ωᵢ * Jᵢ

   原语操作详解:
      Jr: Right Jacobian computation - 9 次写入
      Jr_inv: Right Jacobian inverse - 9 次写入
      RT: Jacobian transpose - 0 次写入
      RR: Jacobian chain rule - 36 次写入
      Hessian块: (Jᵢᵀ * Ωᵢ) * Jᵢ - 72 次写入
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 126
   因子数量: 21
   Hessian总写入: 2,646

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 42 次 × 9 = 378 写入
      Log: 21 次 × 3 = 63 写入
      RT: 63 次 × 0 = 0 写入
      RR: 42 次 × 9 = 378 写入
      RV: 42 次 × 3 = 126 写入
      VP: 63 次 × 0 = 0 写入
      Jr: 42 次 × 9 = 378 写入

🎯 原语操作总写入: 1,323

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 1,470
   Hessian构建 (左侧H): 3,654
   QR分解求解:
     - 预处理: 36
     - 分解: 72
     - 求解: 6
   系统求解总计: 114
   总计: 5,238

🎯 修正后总结
================================================================================
修正后总RRAM写入: 84,264
平均每算法: 16,852

关键修正:
1. 使用正确的Equation (4)公式
2. 反向遍历构建Hessian矩阵
3. 前向遍历计算误差向量
4. 基于Table 3的原语操作
5. SIMD特性: 只有乘法需要写入，加减法/转置不需要写入
6. QR分解多步骤: 预处理 + 分解 + 求解
