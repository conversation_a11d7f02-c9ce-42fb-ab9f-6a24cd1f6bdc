#!/usr/bin/env python3
"""
修正的线性方程系统构建过程分析

基于论文Figure 10, 11和Equation (4)的正确理解：
1. 误差计算 → 右侧向量 b
2. Hessian构建 → 反向遍历因子图 → 左侧矩阵 H
3. 统一位姿表示的正确公式
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class CorrectedLinearSystemAnalyzer:
    """基于论文正确理解的线性系统分析"""
    
    def __init__(self):
        # 基于论文Equation (4)的正确公式
        self.correct_formulas = {
            'unified_pose_operations': {
                'composition': 'ξ1 ⊕ ξ2 = primitive operations',
                'inverse_composition': 'ξ1 ⊖ ξ2 = primitive operations',
                'localization_control_use': 'Θ to calculate deviation between actual observations and ideal models',
                'planning_use': '⊕ to compute world coordinates of points on robot links'
            },
            
            'orientation_position_error': {
                'orientation_error': 'eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)',
                'position_error': 'ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)',
                'variables': {
                    'ΔRᵢⱼ, Rᵢ, Rⱼ': 'rotation matrices of constraint and two poses',
                    'Δtᵢⱼ, tᵢ, tⱼ': 'translation vectors'
                }
            }
        }
        
        # 基于Figure 11的MO-DFG反向遍历过程
        self.mo_dfg_process = {
            'forward_traversal': {
                'description': 'Forward traversal on MO-DFG yields error instructions',
                'process': [
                    ('VP', 'Vector operations', 'error_calculation'),
                    ('RT', 'Rotation transpose', 'intermediate_results'),
                    ('RR', 'Rotation multiplication', 'pose_composition'),
                    ('Log', 'Logarithmic mapping', 'manifold_to_tangent'),
                    ('RV', 'Rotation-vector multiplication', 'transform_operations'),
                    ('VP', 'Final error computation', 'residual_vector')
                ]
            },
            
            'backward_propagation': {
                'description': 'Backward propagation produces derivative instructions',
                'process': [
                    ('Jacobian_computation', 'Compute ∂e/∂x for each variable', 'derivative_calculation'),
                    ('Transpose_jacobian', 'Jᵀ computation', 'gradient_preparation'),
                    ('Information_multiply', 'Jᵀ * Ω', 'weighted_jacobian'),
                    ('Hessian_accumulate', 'H += Jᵀ * Ω * J', 'system_matrix'),
                    ('Gradient_accumulate', 'g += Jᵀ * Ω * e', 'rhs_vector')
                ]
            }
        }
        
        # 正确的线性系统构建过程
        self.correct_linear_system = {
            'system_equation': 'H * Δx = -g',
            'left_side_H': {
                'description': 'Hessian matrix from backward traversal',
                'construction_method': 'reverse_factor_graph_traversal',
                'formula': 'H = Σᵢ (Jᵢᵀ * Ωᵢ * Jᵢ)',
                'process': [
                    ('1_reverse_traversal', 'Traverse factor graph in reverse order'),
                    ('2_jacobian_computation', 'Compute Jacobian for each factor'),
                    ('3_transpose_operation', 'Jᵢᵀ = transpose(Jᵢ)'),
                    ('4_information_multiply', 'Jᵢᵀ * Ωᵢ'),
                    ('5_hessian_block', '(Jᵢᵀ * Ωᵢ) * Jᵢ'),
                    ('6_accumulate', 'H += Jᵢᵀ * Ωᵢ * Jᵢ')
                ]
            },
            
            'right_side_g': {
                'description': 'Gradient vector from error computation',
                'construction_method': 'forward_error_calculation',
                'formula': 'g = Σᵢ (Jᵢᵀ * Ωᵢ * eᵢ)',
                'process': [
                    ('1_forward_traversal', 'Forward traversal computes errors'),
                    ('2_error_calculation', 'eᵢ = f(xᵢ) using primitive operations'),
                    ('3_jacobian_transpose', 'Jᵢᵀ from backward propagation'),
                    ('4_weighted_error', 'Jᵢᵀ * Ωᵢ * eᵢ'),
                    ('5_accumulate', 'g += Jᵢᵀ * Ωᵢ * eᵢ')
                ]
            }
        }
        
        # 基于Table 3的原语操作 + SIMD特性 + QR分解多步骤
        self.primitive_operations = {
            # SIMD操作: 只有乘法需要写入，加减法不需要写入
            'VP': {'description': 'Vector addition (subtraction)', 'rram_writes': 0, 'simd': True},
            'RT': {'description': 'Rotation matrix transpose', 'rram_writes': 0, 'simd': True},  # 转置不需要写入
            'Log': {'description': 'Logarithmic mapping of rotation matrix', 'rram_writes': 3},
            'RR': {'description': 'Rotation matrix multiplication', 'rram_writes': 9},  # 乘法需要写入
            'RV': {'description': 'Rotation matrix-vector multiplication', 'rram_writes': 3},  # 乘法需要写入
            'Exp': {'description': 'Exponential mapping of Lie algebra', 'rram_writes': 9},
            'Skew': {'description': 'Skew-symmetric matrix', 'rram_writes': 0, 'simd': True},  # 构造不需要写入
            'Jr': {'description': 'Right Jacobian', 'rram_writes': 9},
            'Jr_inv': {'description': 'Right Jacobian inverse', 'rram_writes': 9},

            # QR分解多步骤操作
            'QR_preprocessing': {'description': 'QR preprocessing step', 'rram_writes': 'matrix_size'},
            'QR_factorization': {'description': 'QR factorization step', 'rram_writes': 'matrix_size * 2'},
            'QR_solve': {'description': 'QR solve step', 'rram_writes': 'matrix_size'}
        }
    
    def analyze_correct_linear_system(self, app_name: str, algorithm: str):
        """基于正确理解分析线性系统构建"""
        
        print(f"\n🔍 修正分析: {app_name} - {algorithm}")
        print("=" * 80)
        print("基于论文Figure 10, 11和Equation (4)的正确理解")
        print()
        
        # 获取因子图信息
        try:
            nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
            num_factors = len(factors)
        except:
            num_factors = self._estimate_factors(app_name, algorithm)
        
        # 分析正确的构建过程
        self._analyze_correct_error_computation(app_name, algorithm, num_factors)
        self._analyze_correct_hessian_construction(app_name, algorithm, num_factors)
        self._analyze_primitive_operations_usage(app_name, algorithm, num_factors)
        
        return self._calculate_corrected_writes(app_name, algorithm, num_factors)
    
    def _analyze_correct_error_computation(self, app_name: str, algorithm: str, num_factors: int):
        """分析正确的误差计算过程 → 右侧向量b"""
        
        print("📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)")
        print("-" * 60)
        print("基于Figure 11的前向遍历过程:")
        print()
        
        # 基于Equation (4)的误差计算
        if 'localization' in algorithm:
            print("🔹 定位算法误差计算 (使用Θ操作):")
            print("   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)")
            print("   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)")
            print()
            
            # 原语操作序列 (考虑SIMD特性: 只有乘法需要写入)
            operations = [
                ('Exp', 'Rᵢ = Exp(φᵢ)', 9),
                ('Exp', 'Rⱼ = Exp(φⱼ)', 9),
                ('RT', 'Rᵢᵀ = transpose(Rᵢ)', 0),  # SIMD转置不需要写入
                ('RR', 'Rᵢᵀ * Rⱼ', 9),  # 乘法需要写入
                ('RT', 'ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ)', 0),  # SIMD转置不需要写入
                ('RR', 'ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ)', 9),  # 乘法需要写入
                ('Log', 'eφ = Log(result)', 3),
                ('VP', 'tⱼ - tᵢ', 0),  # SIMD向量减法不需要写入
                ('RV', 'Rᵢᵀ * (tⱼ - tᵢ)', 3),  # 乘法需要写入
                ('VP', 'Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ', 0),  # SIMD向量减法不需要写入
                ('RV', 'ep = ΔRᵢⱼᵀ * result', 3)  # 乘法需要写入
            ]
            
        elif 'planning' in algorithm:
            print("🔹 规划算法误差计算 (使用⊕操作):")
            print("   世界坐标计算: 使用⊕计算机器人链接点的世界坐标")
            print()

            operations = [
                ('VP', '路径点差值计算', 0),  # SIMD向量操作不需要写入
                ('RR', '旋转组合', 9),  # 乘法需要写入
                ('VP', '平滑性误差', 0),  # SIMD向量操作不需要写入
                ('Distance', '碰撞检测距离', 10),  # 自定义操作
                ('Min', '最小距离', 0)  # SIMD比较操作不需要写入
            ]
            
        elif 'control' in algorithm:
            print("🔹 控制算法误差计算 (使用Θ操作):")
            print("   偏差计算: 实际观测与理想模型的偏差")
            print()

            operations = [
                ('VP', '状态预测误差', 0),  # SIMD向量操作不需要写入
                ('RR', '动力学模型计算', 9),  # 乘法需要写入
                ('VP', '控制误差', 0)  # SIMD向量操作不需要写入
            ]
        
        total_error_writes = sum(op[2] for op in operations if isinstance(op[2], int))
        total_error_writes *= num_factors
        
        print("   原语操作序列:")
        for op_type, description, writes in operations:
            if isinstance(writes, int):
                if writes == 0:
                    print(f"      {op_type}: {description} - {writes} 次写入 (SIMD不需要写入)")
                else:
                    print(f"      {op_type}: {description} - {writes} 次写入")
            else:
                print(f"      {op_type}: {description}")
        
        print(f"\n🎯 误差计算总结:")
        print(f"   每因子写入: {total_error_writes // num_factors}")
        print(f"   因子数量: {num_factors}")
        print(f"   误差计算总写入: {total_error_writes:,}")
        print()
    
    def _analyze_correct_hessian_construction(self, app_name: str, algorithm: str, num_factors: int):
        """分析正确的Hessian构建过程 → 左侧矩阵H (基于MO-DFG反向求导)"""

        print("📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)")
        print("-" * 60)
        print("基于MO-DFG反向求导的操作过程:")
        print()

        variable_dim = self._get_variable_dimension(app_name, algorithm)

        if 'localization' in algorithm:
            print("🔹 定位算法MO-DFG反向求导过程:")
            print("   从图中可以看到反向求导的具体操作序列:")
            print()
            print("   1️⃣ 从LOG节点开始反向传播:")
            print("      LOG → RR (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ) ← I*")
            print("      输出: I* (单位矩阵的梯度)")
            print()
            print("   2️⃣ RR节点的反向传播:")
            print("      RR → RT (Rᵢᵀ) ← I*I*")
            print("      RR → RR (ΔRᵢⱼᵀRᵢᵀ) ← (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ")
            print("      输出: I*I* 和 (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ")
            print()
            print("   3️⃣ RT节点的反向传播:")
            print("      RT → EXP ← -(Rᵢ)ᵀI*")
            print("      RT → EXP ← -(Rᵢ)ᵀΔ")
            print("      输出: phiᵢ = (0,0,0)ᵀ 和 phiⱼ = I*I*I*")
            print()
            print("   4️⃣ 位置部分的反向传播:")
            print("      RV → VP ← I*(ΔRᵢⱼᵀ)")
            print("      VP → RV ← (ΔRᵢⱼᵀ)*I*")
            print("      输出: tᵢ = (2,1,1,0)ᵀ 和 tⱼ = (1,0,0,2)ᵀ")
            print()
            print("   🔧 使用config matrix StaticVMM:")
            print("      A: Use config matrix StaticVMM")
            print("      这表明Jacobian计算使用预配置的静态矩阵乘法")
            print()

            # 基于MO-DFG反向求导的原语操作序列
            mo_dfg_operations = [
                ('LOG_backward', 'LOG节点反向传播', 3),  # 输出梯度
                ('RR_backward_1', 'RR节点反向传播(路径1)', 9),  # 矩阵乘法梯度
                ('RR_backward_2', 'RR节点反向传播(路径2)', 9),  # 矩阵乘法梯度
                ('RT_backward_1', 'RT节点反向传播(路径1)', 0),  # SIMD转置梯度
                ('RT_backward_2', 'RT节点反向传播(路径2)', 0),  # SIMD转置梯度
                ('EXP_backward_1', 'EXP节点反向传播(phiᵢ)', 9),  # 指数映射梯度
                ('EXP_backward_2', 'EXP节点反向传播(phiⱼ)', 9),  # 指数映射梯度
                ('RV_backward', 'RV节点反向传播', 3),  # 矩阵-向量乘法梯度
                ('VP_backward_1', 'VP节点反向传播(tᵢ)', 0),  # SIMD向量操作梯度
                ('VP_backward_2', 'VP节点反向传播(tⱼ)', 0),  # SIMD向量操作梯度
                ('StaticVMM_config', 'config matrix StaticVMM', variable_dim * 6),  # 静态VMM配置
            ]

        elif 'planning' in algorithm:
            print("🔹 规划算法MO-DFG反向求导过程:")
            print("   基于路径优化的反向传播")
            print("   使用DynamicVMM进行动态Jacobian计算")
            print()

            mo_dfg_operations = [
                ('Path_backward', '路径导数反向传播', variable_dim * 2),
                ('Collision_backward', '碰撞导数反向传播', variable_dim * 3),
                ('DynamicVMM', '动态VMM计算', variable_dim),
            ]

        elif 'control' in algorithm:
            print("🔹 控制算法MO-DFG反向求导过程:")
            print("   基于动力学模型的反向传播")
            print("   使用DynamicVMM进行控制Jacobian计算")
            print()

            mo_dfg_operations = [
                ('Dynamics_backward', '动力学反向传播', variable_dim * variable_dim),
                ('Control_backward', '控制反向传播', variable_dim * 2),
                ('DynamicVMM', '动态VMM计算', variable_dim),
            ]

        jacobian_writes = sum(op[2] for op in mo_dfg_operations)

        # Hessian块计算: J^T * Ω * J (基于MO-DFG输出)
        if 'localization' in algorithm:
            # 基于MO-DFG输出的Jacobian矩阵结构
            J_rows = 6  # 误差维度 (3方向 + 3位置)
            J_cols = 4 * variable_dim  # 4个变量的Jacobian
            # 使用StaticVMM进行Hessian块计算
            JT_omega_writes = J_rows * J_cols  # J^T * Ω (StaticVMM)
            hessian_block_writes = J_cols * J_cols  # (J^T * Ω) * J (StaticVMM)
        else:
            # 其他算法使用DynamicVMM
            J_rows = variable_dim
            J_cols = variable_dim * 2
            JT_omega_writes = J_rows * J_cols  # J^T * Ω (DynamicVMM)
            hessian_block_writes = J_cols * J_cols  # (J^T * Ω) * J (DynamicVMM)

        accumulate_writes = 0  # H += block (SIMD累加不需要写入)

        hessian_per_factor = jacobian_writes + JT_omega_writes + hessian_block_writes + accumulate_writes
        total_hessian_writes = hessian_per_factor * num_factors

        print("   MO-DFG反向求导操作详解:")
        for op_type, description, writes in mo_dfg_operations:
            if writes == 0:
                print(f"      {op_type}: {description} - {writes} 次写入 (SIMD不需要写入)")
            else:
                print(f"      {op_type}: {description} - {writes} 次写入")

        vmm_type = "StaticVMM" if 'localization' in algorithm else "DynamicVMM"
        print(f"      J^T*Ω计算: {JT_omega_writes} 次写入 ({vmm_type})")
        print(f"      Hessian块: (J^T*Ω)*J - {hessian_block_writes} 次写入 ({vmm_type})")
        print(f"      累加操作: H += block - {accumulate_writes} 次写入 (SIMD不需要写入)")

        print(f"\n🎯 Hessian构建总结:")
        print(f"   每因子写入: {hessian_per_factor}")
        print(f"   因子数量: {num_factors}")
        print(f"   Hessian总写入: {total_hessian_writes:,}")
        print()
    
    def _analyze_primitive_operations_usage(self, app_name: str, algorithm: str, num_factors: int):
        """分析原语操作使用情况"""
        
        print("📋 原语操作使用统计 (基于Table 3)")
        print("-" * 60)
        
        # 统计各种原语操作的使用次数
        operation_counts = {}
        
        if 'localization' in algorithm:
            operation_counts = {
                'Exp': num_factors * 2,  # 每因子2个位姿
                'Log': num_factors * 1,  # 每因子1个误差
                'RT': num_factors * 3,   # 转置操作
                'RR': num_factors * 2,   # 旋转乘法
                'RV': num_factors * 2,   # 旋转-向量乘法
                'VP': num_factors * 3,   # 向量操作
                'Jr': num_factors * 2,   # Right Jacobian
            }
        elif 'planning' in algorithm:
            operation_counts = {
                'VP': num_factors * 5,   # 大量向量操作
                'RR': num_factors * 1,   # 少量旋转
                'Distance': num_factors * 10,  # 距离计算
            }
        elif 'control' in algorithm:
            operation_counts = {
                'VP': num_factors * 4,   # 状态向量操作
                'RR': num_factors * 1,   # 动力学旋转
                'Dynamics': num_factors * 5,  # 动力学计算
            }
        
        total_primitive_writes = 0
        
        print("   原语操作使用统计:")
        for op_type, count in operation_counts.items():
            if op_type in self.primitive_operations:
                writes_per_op = self.primitive_operations[op_type]['rram_writes']
                if isinstance(writes_per_op, int):
                    total_writes = count * writes_per_op
                    total_primitive_writes += total_writes
                    print(f"      {op_type}: {count} 次 × {writes_per_op} = {total_writes} 写入")
                else:
                    print(f"      {op_type}: {count} 次 (变长)")
            else:
                print(f"      {op_type}: {count} 次 (自定义操作)")
        
        print(f"\n🎯 原语操作总写入: {total_primitive_writes:,}")
        print()
    
    def _get_variable_dimension(self, app_name: str, algorithm: str) -> int:
        """获取变量维度"""
        dim_map = {
            'robot_localization': 3, 'robot_planning': 6, 'robot_control': 3,
            'manipulator_localization': 2, 'manipulator_planning': 4, 'manipulator_control': 2,
            'autovehicle_localization': 3, 'autovehicle_planning': 6, 'autovehicle_control': 5,
            'quadrotor_localization': 6, 'quadrotor_planning': 12, 'quadrotor_control': 12,
        }
        return dim_map.get(f'{app_name}_{algorithm}', 6)
    
    def _estimate_factors(self, app_name: str, algorithm: str) -> int:
        """估算因子数量"""
        factor_map = {
            'robot_localization': 212, 'robot_planning': 52, 'robot_control': 61,
            'manipulator_localization': 1, 'manipulator_planning': 59, 'manipulator_control': 61,
            'autovehicle_localization': 251, 'autovehicle_planning': 17, 'autovehicle_control': 13,
            'quadrotor_localization': 21, 'quadrotor_planning': 17, 'quadrotor_control': 13,
        }
        return factor_map.get(f'{app_name}_{algorithm}', 50)
    
    def _calculate_corrected_writes(self, app_name: str, algorithm: str, num_factors: int) -> int:
        """计算修正后的总写入次数"""
        
        variable_dim = self._get_variable_dimension(app_name, algorithm)
        
        # 误差计算 (右侧) - 基于SIMD优化后的实际写入
        if 'localization' in algorithm:
            error_per_factor = 45  # 基于SIMD优化后的Equation (4)
        elif 'planning' in algorithm:
            error_per_factor = 19  # 基于SIMD优化
        else:  # control
            error_per_factor = 9   # 基于SIMD优化

        total_error = error_per_factor * num_factors

        # Hessian构建 (左侧，基于MO-DFG反向求导)
        if 'localization' in algorithm:
            # 基于MO-DFG反向求导的操作序列
            mo_dfg_backward = 3 + 9 + 9 + 0 + 0 + 9 + 9 + 3 + 0 + 0  # 反向传播操作
            static_vmm_config = variable_dim * 6  # StaticVMM配置
            J_rows = 6
            J_cols = 4 * variable_dim
            JT_omega = J_rows * J_cols  # StaticVMM
            hessian_block = J_cols * J_cols  # StaticVMM
            hessian_per_factor = mo_dfg_backward + static_vmm_config + JT_omega + hessian_block
        elif 'planning' in algorithm:
            # 规划算法的MO-DFG反向求导
            mo_dfg_backward = variable_dim * 6  # 路径+碰撞反向传播
            dynamic_vmm = variable_dim  # DynamicVMM
            J_rows = variable_dim
            J_cols = variable_dim * 2
            JT_omega = J_rows * J_cols  # DynamicVMM
            hessian_block = J_cols * J_cols  # DynamicVMM
            hessian_per_factor = mo_dfg_backward + dynamic_vmm + JT_omega + hessian_block
        else:  # control
            # 控制算法的MO-DFG反向求导
            mo_dfg_backward = variable_dim * variable_dim + variable_dim * 2  # 动力学+控制反向传播
            dynamic_vmm = variable_dim  # DynamicVMM
            J_rows = variable_dim
            J_cols = variable_dim * 2
            JT_omega = J_rows * J_cols  # DynamicVMM
            hessian_block = J_cols * J_cols  # DynamicVMM
            hessian_per_factor = mo_dfg_backward + dynamic_vmm + JT_omega + hessian_block

        total_hessian = hessian_per_factor * num_factors
        
        # 系统求解 (QR分解多步骤)
        matrix_size = variable_dim * variable_dim
        qr_preprocessing = matrix_size
        qr_factorization = matrix_size * 2
        qr_solve = variable_dim
        solution_writes = qr_preprocessing + qr_factorization + qr_solve
        
        total_writes = total_error + total_hessian + solution_writes
        
        print(f"🎯 修正后的总RRAM写入统计 (考虑SIMD特性):")
        print(f"   误差计算 (右侧b): {total_error:,}")
        print(f"   Hessian构建 (左侧H): {total_hessian:,}")
        print(f"   QR分解求解:")
        print(f"     - 预处理: {qr_preprocessing:,}")
        print(f"     - 分解: {qr_factorization:,}")
        print(f"     - 求解: {qr_solve:,}")
        print(f"   系统求解总计: {solution_writes:,}")
        print(f"   总计: {total_writes:,}")
        print()
        
        return total_writes
    
    def _get_factor_graph(self, app_name: str, algorithm: str):
        """获取因子图"""
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
        }
        
        generator = generators.get((app_name, algorithm))
        if generator:
            return generator()
        else:
            raise ValueError(f"未找到生成器: {app_name} - {algorithm}")


def main():
    """主函数 - 修正的线性系统分析"""
    
    analyzer = CorrectedLinearSystemAnalyzer()
    
    print("🚀 修正的线性方程系统构建过程分析")
    print("=" * 80)
    print("基于论文Figure 10, 11和Equation (4)的正确理解:")
    print("1. 误差计算 → 右侧向量 b (前向遍历)")
    print("2. Hessian构建 → 左侧矩阵 H (反向遍历)")
    print("3. 统一位姿表示的正确公式")
    print("4. 原语操作的准确使用")
    print()
    
    # 分析所有算法
    algorithms = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]
    
    total_corrected_writes = 0
    
    for app_name, algorithm in algorithms:
        try:
            writes = analyzer.analyze_correct_linear_system(app_name, algorithm)
            total_corrected_writes += writes
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")
    
    print(f"🎯 修正后总结")
    print("=" * 80)
    print(f"修正后总RRAM写入: {total_corrected_writes:,}")
    print(f"平均每算法: {total_corrected_writes//len(algorithms):,}")
    print()
    print("关键修正:")
    print("1. 使用正确的Equation (4)公式")
    print("2. 反向遍历构建Hessian矩阵")
    print("3. 前向遍历计算误差向量")
    print("4. 基于Table 3的原语操作")
    print("5. SIMD特性: 只有乘法需要写入，加减法/转置不需要写入")
    print("6. QR分解多步骤: 预处理 + 分解 + 求解")


if __name__ == "__main__":
    main()
