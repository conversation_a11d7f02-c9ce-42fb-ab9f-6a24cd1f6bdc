[{"app_name": "robot", "algorithm": "localization", "total_variables": 70, "total_factors": 212, "matrix_dimensions": [530, 140], "factor_computation_writes": 6338, "matrix_construction_writes": 7950, "qr_decomposition_writes": 312200, "back_substitution_writes": 9940, "total_writes_per_iteration": 336428, "estimated_iterations": 10, "total_writes_complete_solve": 3364280}, {"app_name": "robot", "algorithm": "planning", "total_variables": 30, "total_factors": 52, "matrix_dimensions": [130, 60], "factor_computation_writes": 1516, "matrix_construction_writes": 910, "qr_decomposition_writes": 28200, "back_substitution_writes": 1860, "total_writes_per_iteration": 32486, "estimated_iterations": 15, "total_writes_complete_solve": 487290}, {"app_name": "robot", "algorithm": "control", "total_variables": 39, "total_factors": 61, "matrix_dimensions": [152, 78], "factor_computation_writes": 2347, "matrix_construction_writes": 1337, "qr_decomposition_writes": 41028, "back_substitution_writes": 3120, "total_writes_per_iteration": 47832, "estimated_iterations": 5, "total_writes_complete_solve": 239160}, {"app_name": "manipulator", "algorithm": "localization", "total_variables": 1, "total_factors": 1, "matrix_dimensions": [2, 2], "factor_computation_writes": 8, "matrix_construction_writes": 2, "qr_decomposition_writes": 16, "back_substitution_writes": 4, "total_writes_per_iteration": 30, "estimated_iterations": 8, "total_writes_complete_solve": 240}, {"app_name": "manipulator", "algorithm": "planning", "total_variables": 25, "total_factors": 59, "matrix_dimensions": [147, 50], "factor_computation_writes": 1110, "matrix_construction_writes": 882, "qr_decomposition_writes": 29250, "back_substitution_writes": 1300, "total_writes_per_iteration": 32542, "estimated_iterations": 20, "total_writes_complete_solve": 650840}, {"app_name": "manipulator", "algorithm": "control", "total_variables": 39, "total_factors": 61, "matrix_dimensions": [152, 78], "factor_computation_writes": 2347, "matrix_construction_writes": 1337, "qr_decomposition_writes": 41028, "back_substitution_writes": 3120, "total_writes_per_iteration": 47832, "estimated_iterations": 8, "total_writes_complete_solve": 382656}, {"app_name": "autovehicle", "algorithm": "localization", "total_variables": 60, "total_factors": 251, "matrix_dimensions": [627, 120], "factor_computation_writes": 7508, "matrix_construction_writes": 8151, "qr_decomposition_writes": 333000, "back_substitution_writes": 7320, "total_writes_per_iteration": 355979, "estimated_iterations": 12, "total_writes_complete_solve": 4271748}, {"app_name": "autovehicle", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "matrix_dimensions": [42, 16], "factor_computation_writes": 290, "matrix_construction_writes": 109, "qr_decomposition_writes": 2592, "back_substitution_writes": 144, "total_writes_per_iteration": 3135, "estimated_iterations": 25, "total_writes_complete_solve": 78375}, {"app_name": "autovehicle", "algorithm": "control", "total_variables": 9, "total_factors": 13, "matrix_dimensions": [32, 18], "factor_computation_writes": 632, "matrix_construction_writes": 89, "qr_decomposition_writes": 1908, "back_substitution_writes": 180, "total_writes_per_iteration": 2809, "estimated_iterations": 10, "total_writes_complete_solve": 28090}, {"app_name": "quadrotor", "algorithm": "localization", "total_variables": 15, "total_factors": 21, "matrix_dimensions": [52, 30], "factor_computation_writes": 608, "matrix_construction_writes": 208, "qr_decomposition_writes": 5100, "back_substitution_writes": 480, "total_writes_per_iteration": 6396, "estimated_iterations": 15, "total_writes_complete_solve": 95940}, {"app_name": "quadrotor", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "matrix_dimensions": [42, 16], "factor_computation_writes": 290, "matrix_construction_writes": 109, "qr_decomposition_writes": 2592, "back_substitution_writes": 144, "total_writes_per_iteration": 3135, "estimated_iterations": 30, "total_writes_complete_solve": 94050}, {"app_name": "quadrotor", "algorithm": "control", "total_variables": 9, "total_factors": 13, "matrix_dimensions": [32, 18], "factor_computation_writes": 632, "matrix_construction_writes": 89, "qr_decomposition_writes": 1908, "back_substitution_writes": 180, "total_writes_per_iteration": 2809, "estimated_iterations": 12, "total_writes_complete_solve": 33708}]