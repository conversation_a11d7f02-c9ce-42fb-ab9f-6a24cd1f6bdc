[{"app_name": "robot", "algorithm": "localization", "total_variables": 70, "total_factors": 212, "variable_dimensions": {"poses": 3, "landmarks": 2}, "store_operations_writes": 508, "writeweights_operations_writes": 1335, "static_vmm_operations_writes": 1844, "factorization_operations_writes": 105, "simd_operations_count": 3392, "total_writes_per_iteration": 3792, "estimated_iterations": 12, "total_writes_complete_solve": 45504}, {"app_name": "robot", "algorithm": "planning", "total_variables": 30, "total_factors": 52, "variable_dimensions": {"states": 6}, "store_operations_writes": 124, "writeweights_operations_writes": 327, "static_vmm_operations_writes": 452, "factorization_operations_writes": 45, "simd_operations_count": 832, "total_writes_per_iteration": 948, "estimated_iterations": 15, "total_writes_complete_solve": 14220}, {"app_name": "robot", "algorithm": "control", "total_variables": 39, "total_factors": 61, "variable_dimensions": {"states": 6, "inputs": 3}, "store_operations_writes": 146, "writeweights_operations_writes": 384, "static_vmm_operations_writes": 530, "factorization_operations_writes": 58, "simd_operations_count": 976, "total_writes_per_iteration": 1118, "estimated_iterations": 8, "total_writes_complete_solve": 8944}, {"app_name": "manipulator", "algorithm": "localization", "total_variables": 1, "total_factors": 1, "variable_dimensions": {"states": 6}, "store_operations_writes": 2, "writeweights_operations_writes": 6, "static_vmm_operations_writes": 8, "factorization_operations_writes": 1, "simd_operations_count": 16, "total_writes_per_iteration": 17, "estimated_iterations": 10, "total_writes_complete_solve": 170}, {"app_name": "manipulator", "algorithm": "planning", "total_variables": 25, "total_factors": 59, "variable_dimensions": {"states": 6}, "store_operations_writes": 141, "writeweights_operations_writes": 371, "static_vmm_operations_writes": 513, "factorization_operations_writes": 37, "simd_operations_count": 944, "total_writes_per_iteration": 1062, "estimated_iterations": 20, "total_writes_complete_solve": 21240}, {"app_name": "manipulator", "algorithm": "control", "total_variables": 39, "total_factors": 61, "variable_dimensions": {"states": 6, "inputs": 3}, "store_operations_writes": 146, "writeweights_operations_writes": 384, "static_vmm_operations_writes": 530, "factorization_operations_writes": 58, "simd_operations_count": 976, "total_writes_per_iteration": 1118, "estimated_iterations": 12, "total_writes_complete_solve": 13416}, {"app_name": "autovehicle", "algorithm": "localization", "total_variables": 60, "total_factors": 251, "variable_dimensions": {"poses": 3, "landmarks": 2}, "store_operations_writes": 602, "writeweights_operations_writes": 1581, "static_vmm_operations_writes": 2183, "factorization_operations_writes": 90, "simd_operations_count": 4016, "total_writes_per_iteration": 4456, "estimated_iterations": 15, "total_writes_complete_solve": 66840}, {"app_name": "autovehicle", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "variable_dimensions": {"states": 6}, "store_operations_writes": 40, "writeweights_operations_writes": 107, "static_vmm_operations_writes": 147, "factorization_operations_writes": 12, "simd_operations_count": 272, "total_writes_per_iteration": 306, "estimated_iterations": 25, "total_writes_complete_solve": 7650}, {"app_name": "autovehicle", "algorithm": "control", "total_variables": 9, "total_factors": 13, "variable_dimensions": {"states": 6, "inputs": 3}, "store_operations_writes": 31, "writeweights_operations_writes": 81, "static_vmm_operations_writes": 113, "factorization_operations_writes": 13, "simd_operations_count": 208, "total_writes_per_iteration": 238, "estimated_iterations": 10, "total_writes_complete_solve": 2380}, {"app_name": "quadrotor", "algorithm": "localization", "total_variables": 15, "total_factors": 21, "variable_dimensions": {"poses": 6, "landmarks": 3}, "store_operations_writes": 50, "writeweights_operations_writes": 132, "static_vmm_operations_writes": 182, "factorization_operations_writes": 22, "simd_operations_count": 336, "total_writes_per_iteration": 386, "estimated_iterations": 18, "total_writes_complete_solve": 6948}, {"app_name": "quadrotor", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "variable_dimensions": {"states": 6}, "store_operations_writes": 40, "writeweights_operations_writes": 107, "static_vmm_operations_writes": 147, "factorization_operations_writes": 12, "simd_operations_count": 272, "total_writes_per_iteration": 306, "estimated_iterations": 30, "total_writes_complete_solve": 9180}, {"app_name": "quadrotor", "algorithm": "control", "total_variables": 9, "total_factors": 13, "variable_dimensions": {"states": 6, "inputs": 3}, "store_operations_writes": 31, "writeweights_operations_writes": 81, "static_vmm_operations_writes": 113, "factorization_operations_writes": 13, "simd_operations_count": 208, "total_writes_per_iteration": 238, "estimated_iterations": 15, "total_writes_complete_solve": 3570}]