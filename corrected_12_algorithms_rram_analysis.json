[{"app_name": "robot", "algorithm": "localization", "total_variables": 70, "total_factors": 212, "store_operations_writes": 5088, "acim_operations_writes": 13992, "static_vmm_operations_writes": 18444, "weight_setup_writes": 63, "writes_per_iteration": 37524, "weight_setup_once": 63, "estimated_iterations": 12, "total_writes_complete_solve": 450351}, {"app_name": "robot", "algorithm": "planning", "total_variables": 30, "total_factors": 52, "store_operations_writes": 1248, "acim_operations_writes": 3432, "static_vmm_operations_writes": 4524, "weight_setup_writes": 63, "writes_per_iteration": 9204, "weight_setup_once": 63, "estimated_iterations": 15, "total_writes_complete_solve": 138123}, {"app_name": "robot", "algorithm": "control", "total_variables": 39, "total_factors": 61, "store_operations_writes": 1464, "acim_operations_writes": 4026, "static_vmm_operations_writes": 5307, "weight_setup_writes": 63, "writes_per_iteration": 10797, "weight_setup_once": 63, "estimated_iterations": 8, "total_writes_complete_solve": 86439}, {"app_name": "manipulator", "algorithm": "localization", "total_variables": 1, "total_factors": 1, "store_operations_writes": 24, "acim_operations_writes": 66, "static_vmm_operations_writes": 87, "weight_setup_writes": 63, "writes_per_iteration": 177, "weight_setup_once": 63, "estimated_iterations": 10, "total_writes_complete_solve": 1833}, {"app_name": "manipulator", "algorithm": "planning", "total_variables": 25, "total_factors": 59, "store_operations_writes": 1416, "acim_operations_writes": 3894, "static_vmm_operations_writes": 5133, "weight_setup_writes": 63, "writes_per_iteration": 10443, "weight_setup_once": 63, "estimated_iterations": 20, "total_writes_complete_solve": 208923}, {"app_name": "manipulator", "algorithm": "control", "total_variables": 39, "total_factors": 61, "store_operations_writes": 1464, "acim_operations_writes": 4026, "static_vmm_operations_writes": 5307, "weight_setup_writes": 63, "writes_per_iteration": 10797, "weight_setup_once": 63, "estimated_iterations": 12, "total_writes_complete_solve": 129627}, {"app_name": "autovehicle", "algorithm": "localization", "total_variables": 60, "total_factors": 251, "store_operations_writes": 6024, "acim_operations_writes": 16566, "static_vmm_operations_writes": 21837, "weight_setup_writes": 63, "writes_per_iteration": 44427, "weight_setup_once": 63, "estimated_iterations": 15, "total_writes_complete_solve": 666468}, {"app_name": "autovehicle", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "store_operations_writes": 408, "acim_operations_writes": 1122, "static_vmm_operations_writes": 1479, "weight_setup_writes": 63, "writes_per_iteration": 3009, "weight_setup_once": 63, "estimated_iterations": 25, "total_writes_complete_solve": 75288}, {"app_name": "autovehicle", "algorithm": "control", "total_variables": 9, "total_factors": 13, "store_operations_writes": 312, "acim_operations_writes": 858, "static_vmm_operations_writes": 1131, "weight_setup_writes": 63, "writes_per_iteration": 2301, "weight_setup_once": 63, "estimated_iterations": 10, "total_writes_complete_solve": 23073}, {"app_name": "quadrotor", "algorithm": "localization", "total_variables": 15, "total_factors": 21, "store_operations_writes": 504, "acim_operations_writes": 1386, "static_vmm_operations_writes": 1827, "weight_setup_writes": 63, "writes_per_iteration": 3717, "weight_setup_once": 63, "estimated_iterations": 18, "total_writes_complete_solve": 66969}, {"app_name": "quadrotor", "algorithm": "planning", "total_variables": 8, "total_factors": 17, "store_operations_writes": 408, "acim_operations_writes": 1122, "static_vmm_operations_writes": 1479, "weight_setup_writes": 63, "writes_per_iteration": 3009, "weight_setup_once": 63, "estimated_iterations": 30, "total_writes_complete_solve": 90333}, {"app_name": "quadrotor", "algorithm": "control", "total_variables": 9, "total_factors": 13, "store_operations_writes": 312, "acim_operations_writes": 858, "static_vmm_operations_writes": 1131, "weight_setup_writes": 63, "writes_per_iteration": 2301, "weight_setup_once": 63, "estimated_iterations": 15, "total_writes_complete_solve": 34578}]