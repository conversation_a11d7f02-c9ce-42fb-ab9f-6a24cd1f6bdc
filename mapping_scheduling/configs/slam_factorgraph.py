from typing import Optional
import numpy as np
import networkx as nx
from mapping_scheduling.op_list import Operator, OpList
from mapping_scheduling.scheduling_utils import merge_sub_instr_lists, print_statistics
from utils.utils import deepcopy_with_pickle

def get_scheduling_configs(scheduling_setting: Optional[dict]=None, input_row_parallel_dict: Optional[dict]=None, param: Optional[dict]=None):
    if scheduling_setting is None:
        scheduling_setting = {
            'module_for_StaticVMM': 'acim_l',
            'module_for_DynamicVMM': 'dcim',
        }

    if input_row_parallel_dict is None:
        input_row_parallel_dict = {
            'deltaRij_input': 1,
            'phii_input': 1,
            'phij_input': 1,
            'ti_input': 1,
            'tj_input': 1,
            'deltat_ij_input': 1,
            'A_matrix_input': 1,
        }

    if param is None:
        param = {
            'rotation_dim': 3,       # 3D rotation vector dimension
            'translation_dim': 3,    # 3D translation vector dimension
            'matrix_dim': 3,         # 3x3 rotation matrix dimension
            'N_dim': 6,              # Number of rows in Jacobian matrix A
            'M_dim': 12,             # Number of columns in Jacobian matrix A
            'layers': 1,             # Number of factor graph layers
            'qr_iterations': 5,      # QR decomposition iterations
        }
    
    sub_lists = []
    
    for copy_time in range(param['layers']):  
        # Forward pass
        
        ## Store operations
        ### deltaRij: 3x3 rotation matrix
        globals()['op_list_DeltaRij_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='Store_deltaRij_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_DeltaRij_Input'+str(copy_time)] = {'i_list': globals()['op_list_DeltaRij_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### delta_tij: 3x1 translation vector
        globals()['op_list_DeltaTij_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['translation_dim'], 1),        # 3x1 input
                operation_size=(param['translation_dim'], 1),   # 3x1 output
                name='Store_deltaTij_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_DeltaTij_Input'+str(copy_time)] = {'i_list': globals()['op_list_DeltaTij_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### ti: 3x1 translation vector
        globals()['op_list_Ti_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['translation_dim'], 1),        # 3x1 input
                operation_size=(param['translation_dim'], 1),   # 3x1 output
                name='Store_ti_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_Ti_Input'+str(copy_time)] = {'i_list': globals()['op_list_Ti_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### tj: 3x1 translation vector
        globals()['op_list_Tj_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['translation_dim'], 1),        # 3x1 input
                operation_size=(param['translation_dim'], 1),   # 3x1 output
                name='Store_tj_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_Tj_Input'+str(copy_time)] = {'i_list': globals()['op_list_Tj_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### phii: 3x1 rotation vector
        globals()['op_list_Phii_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['rotation_dim'], 1),       # 3x1 input
                operation_size=(param['rotation_dim'], 1),  # 3x1 output
                name='Store_phii_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_Phii_Input'+str(copy_time)] = {'i_list': globals()['op_list_Phii_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### phij: 3x1 rotation vector
        globals()['op_list_Phij_Input'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['rotation_dim'], 1),       # 3x1 input
                operation_size=(param['rotation_dim'], 1),  # 3x1 output
                name='Store_phij_'+str(copy_time)
            ), [None]],
        ]
        globals()['sub_list_Phij_Input'+str(copy_time)] = {'i_list': globals()['op_list_Phij_Input'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        ## Exp mapping: 3x1 vector -> 3x3 matrix
        ### Ri = exp(phii)
        globals()['op_list_EXP_phii'+str(copy_time)] = [
            [Operator(
                type='Expmapping',
                data_size=(param['rotation_dim'], 1),                       # 3x1 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                NU_input_size=param['rotation_dim'],
                name='EXP_phii_to_Ri_'+str(copy_time),
                concurrent_tag='exp_i_'+str(copy_time)
            ), [(globals()['sub_list_Phii_Input'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_EXP_phii'+str(copy_time)] = {'i_list': globals()['op_list_EXP_phii'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### Rj = exp(phij)
        globals()['op_list_EXP_phij'+str(copy_time)] = [
            [Operator(
                type='Expmapping',
                data_size=(param['rotation_dim'], 1),                       # 3x1 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                NU_input_size=param['rotation_dim'],
                name='EXP_phij_to_Rj_'+str(copy_time),
                concurrent_tag='exp_j_'+str(copy_time)
            ), [(globals()['sub_list_Phij_Input'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_EXP_phij'+str(copy_time)] = {'i_list': globals()['op_list_EXP_phij'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        ## Transpose operations: 3x3 -> 3x3
        ### (deltaRij)^T
        globals()['op_list_RT_deltaRij'+str(copy_time)] = [
            [Operator(
                type='Transpose',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                NU_input_size=param['matrix_dim'] * param['matrix_dim'],    # 9 elements
                name='RT_deltaRij_transpose_'+str(copy_time),
                concurrent_tag='rt_transpose_'+str(copy_time)
            ), [(globals()['sub_list_DeltaRij_Input'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RT_deltaRij'+str(copy_time)] = {'i_list': globals()['op_list_RT_deltaRij'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### (Ri)^T
        globals()['op_list_RT_Ri'+str(copy_time)] = [
            [Operator(
                type='Transpose',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                NU_input_size=param['matrix_dim'] * param['matrix_dim'],    # 9 elements
                name='RT_Ri_transpose_'+str(copy_time),
                concurrent_tag='rt_ri_'+str(copy_time)
            ), [(globals()['sub_list_EXP_phii'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RT_Ri'+str(copy_time)] = {'i_list': globals()['op_list_RT_Ri'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### (Rj)^T
        globals()['op_list_RT_Rj'+str(copy_time)] = [
            [Operator(
                type='Transpose',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                NU_input_size=param['matrix_dim'] * param['matrix_dim'],    # 9 elements
                name='RT_Rj_transpose_'+str(copy_time),
                concurrent_tag='rt_j_'+str(copy_time)
            ), [(globals()['sub_list_EXP_phij'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RT_Rj'+str(copy_time)] = {'i_list': globals()['op_list_RT_Rj'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        ## Matrix multiplications: 3x3 * 3x3 -> 3x3
        ## (deltaRij)^T * Rj
        globals()['op_list_RR1'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='RR_deltaRij_Rj_'+str(copy_time),
                concurrent_tag='rr1_'+str(copy_time)
            ), [(globals()['sub_list_RT_deltaRij'+str(copy_time)], 0), (globals()['sub_list_EXP_phij'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RR1'+str(copy_time)] = {'i_list': globals()['op_list_RR1'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## ((deltaRij)^T * Rj) * (Ri)^T
        globals()['op_list_RR2'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='RR_final_rotation_'+str(copy_time),
                concurrent_tag='rr2_'+str(copy_time)
            ), [(globals()['sub_list_RR1'+str(copy_time)], 0), (globals()['sub_list_RT_Ri'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RR2'+str(copy_time)] = {'i_list': globals()['op_list_RR2'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## Vector operations: 3x1 vectors
        ### ti - tj
        globals()['op_list_VP_ep1'+str(copy_time)] = [
            [Operator(
                type='Subtract',
                data_size=(param['translation_dim'], 1),       # 3x1 input
                operation_size=(param['translation_dim'], 1),  # 3x1 output
                NU_input_size=param['translation_dim'],
                name='VP_ti_minus_tj_'+str(copy_time),
                concurrent_tag='vp_ep1_'+str(copy_time)
            ), [(globals()['sub_list_Ti_Input'+str(copy_time)], 0), (globals()['sub_list_Tj_Input'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_VP_ep1'+str(copy_time)] = {'i_list': globals()['op_list_VP_ep1'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## (Rj)^T * (ti - tj): 3x3 * 3x1 -> 3x1
        globals()['op_list_RV_ep1'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['translation_dim']),    # 3x3 input (matrix) and 3x1 input (vector)
                operation_size=(param['translation_dim'], 1),                 # 3x1 output
                name='RV_RjT_x_t_diff_'+str(copy_time),
                concurrent_tag='rv_ep1_'+str(copy_time)
            ), [(globals()['sub_list_RT_Rj'+str(copy_time)], 0), (globals()['sub_list_VP_ep1'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RV_ep1'+str(copy_time)] = {'i_list': globals()['op_list_RV_ep1'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        ### (Rj)^T * (ti - tj) - delta_t_ij: 3x1 - 3x1 -> 3x1
        globals()['op_list_VP_ep2'+str(copy_time)] = [
            [Operator(
                type='Subtract',
                data_size=(param['translation_dim'], 1),       # 3x1 input
                operation_size=(param['translation_dim'], 1),  # 3x1 output
                NU_input_size=param['translation_dim'],
                name='VP_result_minus_deltaT_'+str(copy_time),
                concurrent_tag='vp_ep2_'+str(copy_time)
            ), [(globals()['sub_list_RV_ep1'+str(copy_time)], 0), (globals()['sub_list_DeltaTij_Input'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_VP_ep2'+str(copy_time)] = {'i_list': globals()['op_list_VP_ep2'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## (deltaRij)^T * result: 3x3 * 3x1 -> 3x1
        globals()['op_list_RV_ep_final'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['translation_dim']),      # 3x3 input (matrix) and 3x1 input (vector)
                operation_size=(param['translation_dim'], 1),                   # 3x1 output
                name='RV_final_translation_error_'+str(copy_time),
                concurrent_tag='rv_ep_final_'+str(copy_time)
            ), [(globals()['sub_list_RT_deltaRij'+str(copy_time)], 0), (globals()['sub_list_VP_ep2'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_RV_ep_final'+str(copy_time)] = {'i_list': globals()['op_list_RV_ep_final'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        ## Log mapping: 3x3 matrix -> 3x1 vector
        ### eo: rotation matrix to vector
        globals()['op_list_LOG'+str(copy_time)] = [
            [Operator(
                type='Logmapping',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['rotation_dim'], 1),                  # 3x1 output
                NU_input_size=param['matrix_dim'] * param['matrix_dim'],    # 9 elements input
                name='LOG_rotation_to_vector_'+str(copy_time),
                concurrent_tag='log_'+str(copy_time)
            ), [(globals()['sub_list_RR2'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_LOG'+str(copy_time)] = {'i_list': globals()['op_list_LOG'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # Backward pass
        ## fij/phii
        ### eo/phii: I
        globals()['op_list_Jacobian_eo_phii'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['rotation_dim'], param['rotation_dim']),       # 3x3 input
                operation_size=(param['rotation_dim'], param['rotation_dim']),  # 3x3 output
                name='Jacobian_eo_phii_identity_'+str(copy_time),
                concurrent_tag='jac_eo_phii_'+str(copy_time)
            ), [None]]
        ]
        globals()['sub_list_Jacobian_eo_phii'+str(copy_time)] = {'i_list': globals()['op_list_Jacobian_eo_phii'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### ep/phii: 0
        globals()['op_list_Jacobian_ep_phii'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['translation_dim'], param['rotation_dim']),       # 3x3 input
                operation_size=(param['translation_dim'], param['rotation_dim']),  # 3x3 output
                name='Jacobian_ep_phii_zero_'+str(copy_time),
                concurrent_tag='jac_ep_phii_'+str(copy_time)
            ), [None]]
        ]
        globals()['sub_list_Jacobian_ep_phii'+str(copy_time)] = {'i_list': globals()['op_list_Jacobian_ep_phii'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## fij/phij
        ## eo/phij: (Ri)T(Rj)T*I
        globals()['op_list_RiT_Rj'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='RiT_x_Rj_'+str(copy_time),
                concurrent_tag='rit_rj_'+str(copy_time)
            ), [(globals()['sub_list_RT_Ri'+str(copy_time)], 0), (globals()['sub_list_RT_Rj'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_Jacobian_eo_phij'+str(copy_time)] = {'i_list': globals()['op_list_RiT_Rj'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### ep/phij: -(ΔRij)T(Rj)T[(ti-tj)^](Rj)
        #### Store 9x3 matrix
        globals()['op_list_skew_config_matrix_'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['rotation_dim'] + param['translation_dim'] + param['rotation_dim'], param['rotation_dim']),       # 9x3 input
                operation_size=(param['rotation_dim'] + param['translation_dim'] + param['rotation_dim'], param['rotation_dim']),  # 9x3 output
                name='Skew_config_Matrix_'+str(copy_time),
                concurrent_tag='skew_config_Matrix_'+str(copy_time)
            ), [None]]
        ]
        globals()['sub_list_skew_config_Matrix_'+str(copy_time)] = {'i_list': globals()['op_list_skew_config_matrix_'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        #### write weights: 9x3 -> weights
        globals()['op_list_WriteWeights_skew_config_Matrix_'+str(copy_time)] = [
            [Operator(
                type='WriteWeights',
                data_size=(param['rotation_dim'] + param['translation_dim'] + param['rotation_dim'], param['rotation_dim']),       # 9x3 input
                operation_size=(param['rotation_dim'] + param['translation_dim'] + param['rotation_dim'], param['rotation_dim']),  # 9x3 output
                name='WriteWeights_skew_config_Matrix_'+str(copy_time),
                concurrent_tag='write_skew_config_'+str(copy_time)
            ), [(globals()['sub_list_skew_config_Matrix_'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_WriteWeights_skew_config_Matrix_'+str(copy_time)] = {'i_list': globals()['op_list_WriteWeights_skew_config_Matrix_'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        #### Skew matrix for (ti-tj): 3x3 skew matrix
        globals()['op_list_skew_ti_minus_tj'+str(copy_time)] = [
            [Operator(
                type='StaticVMM',
                data_size=(param['translation_dim'], 1),                    # 3x1 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='Skew_ti_minus_tj_'+str(copy_time),
                concurrent_tag='skew_ti_tj_'+str(copy_time)
            ), [(globals()['sub_list_WriteWeights_skew_config_Matrix_'+str(copy_time)], 0), (globals()['sub_list_VP_ep1'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_skew_ti_tj'+str(copy_time)] = {'i_list': globals()['op_list_skew_ti_minus_tj'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        #### (deltaRij)T(Rj)T: 3x3 * 3x3 -> 3x3
        globals()['op_list_deltaRijT_RjT'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='DeltaRijT_x_RjT_'+str(copy_time),
                concurrent_tag='delta_rjt_'+str(copy_time)
            ), [(globals()['sub_list_RT_deltaRij'+str(copy_time)], 0), (globals()['sub_list_RT_Rj'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_deltaRijT_RjT'+str(copy_time)] = {'i_list': globals()['op_list_deltaRijT_RjT'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### (ΔRij)T(Rj)T[(ti-tj)^]: 3x3 * 3x3 -> 3x3
        globals()['op_list_deltaRi_RjT_skew'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='Skew_x_Rj_'+str(copy_time),
                concurrent_tag='skew_rj_'+str(copy_time)
            ), [(globals()['sub_list_deltaRijT_RjT'+str(copy_time)], 0), (globals()['sub_list_skew_ti_tj'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_deltaRi_RjT_skew'+str(copy_time)] = {'i_list': globals()['op_list_deltaRi_RjT_skew'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### (ΔRij)T(Rj)T[(ti-tj)^](Rj)T: 3x3 * 3x3 -> 3x3
        globals()['op_list_deltaRi_RjT_skew_RjT'+str(copy_time)] = [
            [Operator(
                type='DynamicVMM',
                data_size=(param['matrix_dim'], param['matrix_dim']),       # 3x3 input
                operation_size=(param['matrix_dim'], param['matrix_dim']),  # 3x3 output
                name='RjT_x_skew_x_Rj_'+str(copy_time),
                concurrent_tag='rjt_skew_rj_'+str(copy_time)
            ), [(globals()['sub_list_deltaRi_RjT_skew'+str(copy_time)], 0), (globals()['sub_list_RT_Rj'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_Jacobian_ep_phij'+str(copy_time)] = {'i_list': globals()['op_list_deltaRi_RjT_skew_RjT'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## fij/Ti
        ### eo/Ti: 0
        globals()['sub_list_Jacobian_eo_Ti'+str(copy_time)] = {'i_list': globals()['op_list_Jacobian_ep_phii'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### ep/Ti: (ΔRij)T(Rj)T
        globals()['sub_list_Jacobian_ep_Ti'+str(copy_time)] = {'i_list': globals()['op_list_deltaRijT_RjT'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ## fij/Tj
        ### eo/Tj: 0
        globals()['sub_list_Jacobian_eo_Tj'+str(copy_time)] = {'i_list': globals()['op_list_Jacobian_ep_phii'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        ### ep/Tj: -(ΔRij)T(Rj)T
        globals()['op_list_Jacobian_ep_Tj'+str(copy_time)] = [
            [Operator(
                type='Subtract',  # Use subtract from zero to negate
                data_size=(param['translation_dim'], param['translation_dim']),         # 3x3 input
                operation_size=(param['translation_dim'], param['translation_dim']),    # 3x3 output
                NU_input_size=param['translation_dim'] * param['translation_dim'],
                name='Jacobian_ep_Tj_negative_'+str(copy_time),
                concurrent_tag='jac_ep_tj_'+str(copy_time)
            ), [ (globals()['sub_list_Jacobian_eo_Tj'+str(copy_time)], 0), (globals()['sub_list_Jacobian_ep_Ti'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_Jacobian_ep_Tj'+str(copy_time)] = {'i_list': globals()['op_list_Jacobian_ep_Tj'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # Information matrix Omega: 6x6 matrix
        globals()['op_list_Info_Matrix'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['rotation_dim'] + param['translation_dim'], param['rotation_dim'] + param['translation_dim']),       # 6x6 input
                operation_size=(param['rotation_dim'] + param['translation_dim'], param['rotation_dim'] + param['translation_dim']),  # 6x6 output
                name='Info_Matrix_Omega_'+str(copy_time),
                concurrent_tag='info_matrix_'+str(copy_time)
            ), [None]]
        ]
        globals()['sub_list_Info_Matrix'+str(copy_time)] = {'i_list': globals()['op_list_Info_Matrix'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # Write Omega as weights: 6x6 -> weights
        globals()['op_list_WriteWeight_Omega'+str(copy_time)] = [
            [Operator(
                type='WriteWeights',
                data_size=(param['rotation_dim'] + param['translation_dim'], param['rotation_dim'] + param['translation_dim']),       # 6x6 input
                operation_size=(param['rotation_dim'] + param['translation_dim'], param['rotation_dim'] + param['translation_dim']),  # 6x6 output
                name='WriteWeight_Omega_'+str(copy_time),
                concurrent_tag='write_omega_'+str(copy_time)
            ), [(globals()['sub_list_Info_Matrix'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_WriteWeight_Omega'+str(copy_time)] = {'i_list': globals()['op_list_WriteWeight_Omega'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # b = -Omega * [eo; ep]: 6x6 * 6x1 -> 6x1
        globals()['op_list_weighted_error'+str(copy_time)] = [
            [Operator(
                type='StaticVMM',
                data_size=(param['rotation_dim'] + param['translation_dim'], 1),       # 6x1 input
                operation_size=(param['rotation_dim'] + param['translation_dim'], 1),  # 6x1 output
                name='Weighted_Error_'+str(copy_time),
                concurrent_tag='weighted_error_'+str(copy_time)
            ), [(globals()['sub_list_WriteWeight_Omega'+str(copy_time)], 0),
                (globals()['sub_list_LOG'+str(copy_time)], 0),
                (globals()['sub_list_RV_ep_final'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_weighted_error'+str(copy_time)] = {'i_list': globals()['op_list_weighted_error'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # A = -Omega * J: 6x6 * 6x12 -> 6x12
        globals()['op_list_weighted_J'+str(copy_time)] = [
            [Operator(
                type='StaticVMM',
                data_size=(param['rotation_dim'] + param['translation_dim'], param['M_dim']),       # 6x12 input
                operation_size=(param['rotation_dim'] + param['translation_dim'], param['M_dim']),  # 6x12 output
                name='Weighted_Jacobian_'+str(copy_time),
                concurrent_tag='weighted_jacobian_'+str(copy_time)
            ), [(globals()['sub_list_WriteWeight_Omega'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_eo_phii'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_ep_phii'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_eo_phij'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_ep_phij'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_eo_Ti'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_ep_Ti'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_eo_Tj'+str(copy_time)], 0),
                (globals()['sub_list_Jacobian_ep_Tj'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_weighted_J'+str(copy_time)] = {'i_list': globals()['op_list_weighted_J'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        # Same operation steps by using Transpose
        globals()['op_list_augmented_matrix'+str(copy_time)] = [
            [Operator(
                type='Transpose',
                data_size=(param['rotation_dim'] + param['translation_dim'], param['M_dim'] + 1),       # 6x13 input
                operation_size=(param['rotation_dim'] + param['translation_dim'], param['M_dim'] + 1),  # 6x13 output
                name='Augmented_Matrix_'+str(copy_time),
                concurrent_tag='augmented_matrix_'+str(copy_time)
            ), [(globals()['sub_list_weighted_J'+str(copy_time)], 0),
                (globals()['sub_list_weighted_error'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_augmented_matrix'+str(copy_time)] = {'i_list': globals()['op_list_augmented_matrix'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}
        
        # Iterative QR decomposition

        N = param['N_dim']
        M = param['M_dim']
        
        op_list_QR_Decomposition = []
    
        initial_dependency = [
            (globals()['sub_list_augmented_matrix' + str(copy_time)], 0)
        ]

        row_dependencies = {}
        
        all_required_operations = []
        
        for col in range(min(N-1, M)):
            for target_row in range(col + 1, N):
                all_required_operations.append({
                    'col': col,
                    'pivot_row': col,
                    'target_row': target_row,
                    'priority': col * N + target_row, # smaller one has priority
                    'element_pos': (target_row, col)  
                })
        
        print(f"Total {len(all_required_operations)} operations required for QR decomposition with {N} rows and {M} columns.")
        
        # use this definition to create groups of operations that can be executed in parallel
        def create_parallel_groups(operations):
            remaining_ops = operations.copy()
            parallel_stages = []
            
            while remaining_ops:
                current_parallel_group = []
                used_rows = set()
                ops_to_remove = []
                
                remaining_ops.sort(key=lambda x: x['priority'])
                
                for op in remaining_ops:
                    op_rows = {op['pivot_row'], op['target_row']}
                    if not op_rows.intersection(used_rows): # if there is no overlap with used rows
                        current_parallel_group.append(op)   # add this op in this parallel group
                        used_rows.update(op_rows)           
                        ops_to_remove.append(op)            # remove this op out
                
                for op in ops_to_remove:
                    remaining_ops.remove(op)
                
                if current_parallel_group:
                    parallel_stages.append(current_parallel_group)
                    # print(f"Stage {len(parallel_stages)}: Number of operations {len(current_parallel_group)} ")
                    # for op in current_parallel_group:
                    #     print(f"  - used ({op['pivot_row']},{op['target_row']}) to eliminate ({op['target_row']},{op['col']}),")
            
            return parallel_stages
        
        parallel_stages = create_parallel_groups(all_required_operations)
        
        for stage_idx, parallel_group in enumerate(parallel_stages):
            
            for op_info in parallel_group:
                pivot_row = op_info['pivot_row']
                target_row = op_info['target_row']
                col = op_info['col']
                givens_dependencies = []
                if stage_idx == 0:
                    # if in the first stage, use the Matrix from outside
                    givens_dependencies = initial_dependency
                else:
                    # depend on row dependencies
                    deps = set()
                    if pivot_row in row_dependencies:
                        deps.add(row_dependencies[pivot_row])
                    if target_row in row_dependencies:
                        deps.add(row_dependencies[target_row])
                    
                    if deps:
                        givens_dependencies = list(deps)
                    else:
                        givens_dependencies = initial_dependency
                
                base_op_idx = len(op_list_QR_Decomposition)
                
                concurrent_tag_base = f'qr_stage{stage_idx}_g0_{copy_time}'
                
                # Step 1: Preprocessing - compute a_ii^2 + a_ji^2 (sum of squares)
                preprocessing_op = [
                    Operator(
                        type='Preprocessing',
                        data_size=(2, 1),    # 2x1 input (pivot and target elements)
                        operation_size=(1, 1),  # 1x1 output (sum of squares)
                        NU_input_size=2,
                        name=f'QR_Preprocessing_S{stage_idx}_C{col}_R{pivot_row}{target_row}_{copy_time}',
                        concurrent_tag=f'{concurrent_tag_base}_prep'
                    ),
                    givens_dependencies
                ]
                op_list_QR_Decomposition.append(preprocessing_op)
                preprocessing_idx = base_op_idx

                # Step 2: Factorization - compute sqrt() and calculate Givens parameters (c, s)
                givens_params_op = [
                    Operator(
                        type='Factorization',   # This does sqrt(a_ii^2 + a_ji^2) and computes c, s
                        data_size=(3, 1),       # 3x1 input (a_ii, a_ji, sum of squares)
                        operation_size=(2, 1),  # 2x1 output (cos(θ) and sin(θ))
                        NU_input_size=3,        # a_ii, a_ji, and (a_ii^2 + a_ji^2)
                        name=f'QR_Givens_S{stage_idx}_C{col}_R{pivot_row}{target_row}_{copy_time}',
                        concurrent_tag=f'{concurrent_tag_base}_fact'
                    ),
                    givens_dependencies + [preprocessing_idx]  # Depends on both original data and preprocessing
                ]
                op_list_QR_Decomposition.append(givens_params_op)
                givens_params_idx = base_op_idx + 1

                # Step 3: Apply Givens rotation using DynamicVMM
                rotation_dependencies = givens_dependencies + [preprocessing_idx, givens_params_idx]
                matrix_rotation_op = [
                    Operator(
                        type='DynamicVMM',
                        data_size=(2, param['M_dim'] + 1 - col),       # 2x(M+1-col) input
                        operation_size=(2, param['M_dim'] + 1 - col),  # 2x(M+1-col) output
                        name=f'QR_Rotate_S{stage_idx}_C{col}_R{pivot_row}{target_row}_{copy_time}',
                        concurrent_tag=f'{concurrent_tag_base}_vmm'
                    ),
                    rotation_dependencies
                ]
                op_list_QR_Decomposition.append(matrix_rotation_op)
                matrix_rotation_idx = base_op_idx + 2
                
                # Update row dependencies: both rows are now dependent on this rotation
                row_dependencies[pivot_row] = matrix_rotation_idx
                row_dependencies[target_row] = matrix_rotation_idx
        
        # Create the main QR decomposition sub_list
        globals()['op_list_QR_Decomposition' + str(copy_time)] = op_list_QR_Decomposition
        globals()['sub_list_QR_Decomposition' + str(copy_time)] = {
            'i_list': globals()['op_list_QR_Decomposition' + str(copy_time)], 
            'original_len': len(op_list_QR_Decomposition), 
            'input_row_reuse': 1
        }
    
        # Store final R matrix
        if not op_list_QR_Decomposition:
            final_qr_dependency = initial_dependency
            print(f"Warning: No QR decomposition operations found for copy_time {copy_time}. Using initial dependency.")
        else:
            # Depend on all final row operations
            final_ops = set(row_dependencies.values())
            if final_ops:
                final_qr_dependency = [(globals()['sub_list_QR_Decomposition' + str(copy_time)], idx) 
                    for idx in sorted(final_ops)]
            else:
                final_qr_dependency = initial_dependency

        globals()['op_list_QR_Output'+str(copy_time)] = [
            [Operator(
                type='Store',
                data_size=(param['N_dim'], param['M_dim'] + 1),       # 6x13 input
                operation_size=(param['N_dim'], param['M_dim'] + 1),  # 6x13 output
                name='Store_R_matrix_'+str(copy_time),
                concurrent_tag='r_matrix_'+str(copy_time)
            ), final_qr_dependency]
        ]
        globals()['sub_list_QR_Output'+str(copy_time)] = {'i_list': globals()['op_list_QR_Output'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}

        # Back substitution, depend on the number of variables

        globals()['Backsub'+str(copy_time)] = [
            [Operator(
                type='Backsubstitution',
                data_size=(param['N_dim'], param['M_dim'] + 1),    # 6x13 input
                operation_size=(param['M_dim'], 1),                # 12x1 output (solution vector)
                name='Backsubstitution_'+str(copy_time),
                concurrent_tag='backsub_'+str(copy_time)
            ), [(globals()['sub_list_QR_Output'+str(copy_time)], 0)]]
        ]
        globals()['sub_list_Backsub'+str(copy_time)] = {'i_list': globals()['Backsub'+str(copy_time)], 'original_len': 1, 'input_row_reuse': 1}


        # Collect all sub_lists for all
        single_sub_list = [
            # Input operations
            globals()['sub_list_DeltaRij_Input'+str(copy_time)],     
            globals()['sub_list_Phii_Input'+str(copy_time)], 
            globals()['sub_list_Phij_Input'+str(copy_time)],
            globals()['sub_list_Ti_Input'+str(copy_time)],
            globals()['sub_list_Tj_Input'+str(copy_time)],
            globals()['sub_list_DeltaTij_Input'+str(copy_time)],
            globals()['sub_list_Info_Matrix'+str(copy_time)],
            globals()['sub_list_skew_config_Matrix_'+str(copy_time)],  
            
            # Forward pass - Error computation
            globals()['sub_list_EXP_phii'+str(copy_time)],          
            globals()['sub_list_EXP_phij'+str(copy_time)],
            globals()['sub_list_RT_deltaRij'+str(copy_time)],        
            globals()['sub_list_RT_Ri'+str(copy_time)],
            globals()['sub_list_RT_Rj'+str(copy_time)],
            
            # # WriteWeights for StaticVMM operations
            # globals()['sub_list_WriteWeights_deltaRijT'+str(copy_time)],
            # globals()['sub_list_WriteWeights_RR1'+str(copy_time)],
            # globals()['sub_list_WriteWeights_RjT'+str(copy_time)],
            # globals()['sub_list_WriteWeights_RiT'+str(copy_time)],
            # globals()['sub_list_WriteWeights_deltaRijT_RjT'+str(copy_time)],
            # globals()['sub_list_WriteWeights_deltaRi_RjT_skew'+str(copy_time)],
            
            globals()['sub_list_RR1'+str(copy_time)],                
            globals()['sub_list_RR2'+str(copy_time)],                
            globals()['sub_list_VP_ep1'+str(copy_time)],
            globals()['sub_list_RV_ep1'+str(copy_time)],             
            globals()['sub_list_VP_ep2'+str(copy_time)],
            globals()['sub_list_RV_ep_final'+str(copy_time)],
            globals()['sub_list_LOG'+str(copy_time)],
            
            # Backward pass - Jacobian computation
            globals()['sub_list_Jacobian_eo_phii'+str(copy_time)],
            globals()['sub_list_Jacobian_ep_phii'+str(copy_time)],
            globals()['sub_list_Jacobian_eo_phij'+str(copy_time)],
            globals()['sub_list_WriteWeights_skew_config_Matrix_'+str(copy_time)],
            globals()['sub_list_skew_ti_tj'+str(copy_time)],        
            globals()['sub_list_deltaRijT_RjT'+str(copy_time)],     
            globals()['sub_list_deltaRi_RjT_skew'+str(copy_time)],  
            globals()['sub_list_Jacobian_ep_phij'+str(copy_time)],
            globals()['sub_list_Jacobian_eo_Ti'+str(copy_time)],
            globals()['sub_list_Jacobian_ep_Ti'+str(copy_time)],
            globals()['sub_list_Jacobian_eo_Tj'+str(copy_time)],
            globals()['sub_list_Jacobian_ep_Tj'+str(copy_time)],
            
            # Information matrix and weightin
            globals()['sub_list_WriteWeight_Omega'+str(copy_time)],   
            
            globals()['sub_list_weighted_J'+str(copy_time)],
            globals()['sub_list_weighted_error'+str(copy_time)],
            globals()['sub_list_augmented_matrix'+str(copy_time)],
            
            # QR decomposition
            globals()['sub_list_QR_Decomposition'+str(copy_time)],
            globals()['sub_list_QR_Output'+str(copy_time)],

            # Backsubstitution
            globals()['sub_list_Backsub'+str(copy_time)]
        ]
        sub_lists = sub_lists + single_sub_list

    merged_list, statistics_dict = merge_sub_instr_lists(sub_lists, 'Operator')
    print_statistics(statistics_dict, 'Operator')
    
    ops_SLAM = []
    dependency_list_SLAM = []
    for op_info in merged_list:
        ops_SLAM.append(op_info[0])
        dependency_list_SLAM.append(op_info[1])

    return scheduling_setting, ops_SLAM, dependency_list_SLAM, input_row_parallel_dict