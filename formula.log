🚀 修正的线性方程系统构建过程分析
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解:
1. 误差计算 → 右侧向量 b (前向遍历)
2. Hessian构建 → 左侧矩阵 H (反向遍历)
3. 统一位姿表示的正确公式
4. 原语操作的准确使用


🔍 修正分析: robot - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 212
   误差计算总写入: 9,540

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于MO-DFG反向求导的操作过程:

🔹 定位算法MO-DFG反向求导过程:
   从图中可以看到反向求导的具体操作序列:

   1️⃣ 从LOG节点开始反向传播:
      LOG → RR (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ) ← I*
      输出: I* (单位矩阵的梯度)

   2️⃣ RR节点的反向传播:
      RR → RT (Rᵢᵀ) ← I*I*
      RR → RR (ΔRᵢⱼᵀRᵢᵀ) ← (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ
      输出: I*I* 和 (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ

   3️⃣ RT节点的反向传播:
      RT → EXP ← -(Rᵢ)ᵀI*
      RT → EXP ← -(Rᵢ)ᵀΔ
      输出: phiᵢ = (0,0,0)ᵀ 和 phiⱼ = I*I*I*

   4️⃣ 位置部分的反向传播:
      RV → VP ← I*(ΔRᵢⱼᵀ)
      VP → RV ← (ΔRᵢⱼᵀ)*I*
      输出: tᵢ = (2,1,1,0)ᵀ 和 tⱼ = (1,0,0,2)ᵀ

   🔧 使用config matrix StaticVMM:
      A: Use config matrix StaticVMM
      这表明Jacobian计算使用预配置的静态矩阵乘法

   MO-DFG反向求导操作详解:
      LOG_backward: LOG节点反向传播 - 3 次写入
      RR_backward_1: RR节点反向传播(路径1) - 9 次写入
      RR_backward_2: RR节点反向传播(路径2) - 9 次写入
      RT_backward_1: RT节点反向传播(路径1) - 0 次写入 (SIMD不需要写入)
      RT_backward_2: RT节点反向传播(路径2) - 0 次写入 (SIMD不需要写入)
      EXP_backward_1: EXP节点反向传播(phiᵢ) - 9 次写入
      EXP_backward_2: EXP节点反向传播(phiⱼ) - 9 次写入
      RV_backward: RV节点反向传播 - 3 次写入
      VP_backward_1: VP节点反向传播(tᵢ) - 0 次写入 (SIMD不需要写入)
      VP_backward_2: VP节点反向传播(tⱼ) - 0 次写入 (SIMD不需要写入)
      StaticVMM_config: config matrix StaticVMM - 18 次写入
      J^T*Ω计算: 72 次写入 (StaticVMM)
      Hessian块: (J^T*Ω)*J - 144 次写入 (StaticVMM)
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 276
   因子数量: 212
   Hessian总写入: 58,512

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 424 次 × 0 = 0 写入
      Log: 212 次 × 0 = 0 写入
      RT: 636 次 × 0 = 0 写入
      RR: 424 次 × 9 = 3816 写入
      RV: 424 次 × 3 = 1272 写入
      VP: 636 次 × 0 = 0 写入
      Jr: 424 次 × 9 = 3816 写入

🎯 原语操作总写入: 8,904

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 9,540
   Hessian构建 (左侧H): 58,512
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 68,082


🔍 修正分析: robot - planning
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 规划算法误差计算 (使用⊕操作):
   世界坐标计算: 使用⊕计算机器人链接点的世界坐标

   原语操作序列:
      VP: 路径点差值计算 - 0 次写入 (SIMD不需要写入)
      RR: 旋转组合 - 9 次写入
      VP: 平滑性误差 - 0 次写入 (SIMD不需要写入)
      Distance: 碰撞检测距离 - 10 次写入
      Min: 最小距离 - 0 次写入 (SIMD不需要写入)

🎯 误差计算总结:
   每因子写入: 19
   因子数量: 52
   误差计算总写入: 988

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于MO-DFG反向求导的操作过程:

🔹 规划算法MO-DFG反向求导过程:
   基于路径优化的反向传播
   使用DynamicVMM进行动态Jacobian计算

   MO-DFG反向求导操作详解:
      Path_backward: 路径导数反向传播 - 12 次写入
      Collision_backward: 碰撞导数反向传播 - 18 次写入
      DynamicVMM: 动态VMM计算 - 6 次写入
      J^T*Ω计算: 72 次写入 (DynamicVMM)
      Hessian块: (J^T*Ω)*J - 144 次写入 (DynamicVMM)
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 252
   因子数量: 52
   Hessian总写入: 13,104

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      VP: 260 次 × 0 = 0 写入
      RR: 52 次 × 9 = 468 写入
      Distance: 520 次 (自定义操作)

🎯 原语操作总写入: 468

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 988
   Hessian构建 (左侧H): 13,416
   QR分解求解:
     - 预处理: 36
     - 分解: 72
     - 求解: 6
   系统求解总计: 114
   总计: 14,518


🔍 修正分析: robot - control
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 控制算法误差计算 (使用Θ操作):
   偏差计算: 实际观测与理想模型的偏差

   原语操作序列:
      VP: 状态预测误差 - 0 次写入 (SIMD不需要写入)
      RR: 动力学模型计算 - 9 次写入
      VP: 控制误差 - 0 次写入 (SIMD不需要写入)

🎯 误差计算总结:
   每因子写入: 9
   因子数量: 61
   误差计算总写入: 549

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于MO-DFG反向求导的操作过程:

🔹 控制算法MO-DFG反向求导过程:
   基于动力学模型的反向传播
   使用DynamicVMM进行控制Jacobian计算

   MO-DFG反向求导操作详解:
      Dynamics_backward: 动力学反向传播 - 9 次写入
      Control_backward: 控制反向传播 - 6 次写入
      DynamicVMM: 动态VMM计算 - 3 次写入
      J^T*Ω计算: 18 次写入 (DynamicVMM)
      Hessian块: (J^T*Ω)*J - 36 次写入 (DynamicVMM)
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 72
   因子数量: 61
   Hessian总写入: 4,392

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      VP: 244 次 × 0 = 0 写入
      RR: 61 次 × 9 = 549 写入
      Dynamics: 305 次 (自定义操作)

🎯 原语操作总写入: 549

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 549
   Hessian构建 (左侧H): 4,392
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 4,971


🔍 修正分析: autovehicle - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 251
   误差计算总写入: 11,295

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于MO-DFG反向求导的操作过程:

🔹 定位算法MO-DFG反向求导过程:
   从图中可以看到反向求导的具体操作序列:

   1️⃣ 从LOG节点开始反向传播:
      LOG → RR (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ) ← I*
      输出: I* (单位矩阵的梯度)

   2️⃣ RR节点的反向传播:
      RR → RT (Rᵢᵀ) ← I*I*
      RR → RR (ΔRᵢⱼᵀRᵢᵀ) ← (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ
      输出: I*I* 和 (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ

   3️⃣ RT节点的反向传播:
      RT → EXP ← -(Rᵢ)ᵀI*
      RT → EXP ← -(Rᵢ)ᵀΔ
      输出: phiᵢ = (0,0,0)ᵀ 和 phiⱼ = I*I*I*

   4️⃣ 位置部分的反向传播:
      RV → VP ← I*(ΔRᵢⱼᵀ)
      VP → RV ← (ΔRᵢⱼᵀ)*I*
      输出: tᵢ = (2,1,1,0)ᵀ 和 tⱼ = (1,0,0,2)ᵀ

   🔧 使用config matrix StaticVMM:
      A: Use config matrix StaticVMM
      这表明Jacobian计算使用预配置的静态矩阵乘法

   MO-DFG反向求导操作详解:
      LOG_backward: LOG节点反向传播 - 3 次写入
      RR_backward_1: RR节点反向传播(路径1) - 9 次写入
      RR_backward_2: RR节点反向传播(路径2) - 9 次写入
      RT_backward_1: RT节点反向传播(路径1) - 0 次写入 (SIMD不需要写入)
      RT_backward_2: RT节点反向传播(路径2) - 0 次写入 (SIMD不需要写入)
      EXP_backward_1: EXP节点反向传播(phiᵢ) - 9 次写入
      EXP_backward_2: EXP节点反向传播(phiⱼ) - 9 次写入
      RV_backward: RV节点反向传播 - 3 次写入
      VP_backward_1: VP节点反向传播(tᵢ) - 0 次写入 (SIMD不需要写入)
      VP_backward_2: VP节点反向传播(tⱼ) - 0 次写入 (SIMD不需要写入)
      StaticVMM_config: config matrix StaticVMM - 18 次写入
      J^T*Ω计算: 72 次写入 (StaticVMM)
      Hessian块: (J^T*Ω)*J - 144 次写入 (StaticVMM)
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 276
   因子数量: 251
   Hessian总写入: 69,276

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 502 次 × 0 = 0 写入
      Log: 251 次 × 0 = 0 写入
      RT: 753 次 × 0 = 0 写入
      RR: 502 次 × 9 = 4518 写入
      RV: 502 次 × 3 = 1506 写入
      VP: 753 次 × 0 = 0 写入
      Jr: 502 次 × 9 = 4518 写入

🎯 原语操作总写入: 10,542

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 11,295
   Hessian构建 (左侧H): 69,276
   QR分解求解:
     - 预处理: 9
     - 分解: 18
     - 求解: 3
   系统求解总计: 30
   总计: 80,601


🔍 修正分析: quadrotor - localization
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解

📋 右侧向量构建: b = Σᵢ(Jᵢᵀ * Ωᵢ * eᵢ)
------------------------------------------------------------
基于Figure 11的前向遍历过程:

🔹 定位算法误差计算 (使用Θ操作):
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)

   原语操作序列:
      Exp: Rᵢ = Exp(φᵢ) - 9 次写入
      Exp: Rⱼ = Exp(φⱼ) - 9 次写入
      RT: Rᵢᵀ = transpose(Rᵢ) - 0 次写入 (SIMD不需要写入)
      RR: Rᵢᵀ * Rⱼ - 9 次写入
      RT: ΔRᵢⱼᵀ = transpose(ΔRᵢⱼ) - 0 次写入 (SIMD不需要写入)
      RR: ΔRᵢⱼᵀ * (Rᵢᵀ * Rⱼ) - 9 次写入
      Log: eφ = Log(result) - 3 次写入
      VP: tⱼ - tᵢ - 0 次写入 (SIMD不需要写入)
      RV: Rᵢᵀ * (tⱼ - tᵢ) - 3 次写入
      VP: Rᵢᵀ * (tⱼ - tᵢ) - Δtᵢⱼ - 0 次写入 (SIMD不需要写入)
      RV: ep = ΔRᵢⱼᵀ * result - 3 次写入

🎯 误差计算总结:
   每因子写入: 45
   因子数量: 21
   误差计算总写入: 945

📋 左侧矩阵构建: H = Σᵢ(Jᵢᵀ * Ωᵢ * Jᵢ)
------------------------------------------------------------
基于MO-DFG反向求导的操作过程:

🔹 定位算法MO-DFG反向求导过程:
   从图中可以看到反向求导的具体操作序列:

   1️⃣ 从LOG节点开始反向传播:
      LOG → RR (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ) ← I*
      输出: I* (单位矩阵的梯度)

   2️⃣ RR节点的反向传播:
      RR → RT (Rᵢᵀ) ← I*I*
      RR → RR (ΔRᵢⱼᵀRᵢᵀ) ← (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ
      输出: I*I* 和 (ΔRᵢⱼᵀ(Rᵢ)ᵀRᵢ)ᵀ

   3️⃣ RT节点的反向传播:
      RT → EXP ← -(Rᵢ)ᵀI*
      RT → EXP ← -(Rᵢ)ᵀΔ
      输出: phiᵢ = (0,0,0)ᵀ 和 phiⱼ = I*I*I*

   4️⃣ 位置部分的反向传播:
      RV → VP ← I*(ΔRᵢⱼᵀ)
      VP → RV ← (ΔRᵢⱼᵀ)*I*
      输出: tᵢ = (2,1,1,0)ᵀ 和 tⱼ = (1,0,0,2)ᵀ

   🔧 使用config matrix StaticVMM:
      A: Use config matrix StaticVMM
      这表明Jacobian计算使用预配置的静态矩阵乘法

   MO-DFG反向求导操作详解:
      LOG_backward: LOG节点反向传播 - 3 次写入
      RR_backward_1: RR节点反向传播(路径1) - 9 次写入
      RR_backward_2: RR节点反向传播(路径2) - 9 次写入
      RT_backward_1: RT节点反向传播(路径1) - 0 次写入 (SIMD不需要写入)
      RT_backward_2: RT节点反向传播(路径2) - 0 次写入 (SIMD不需要写入)
      EXP_backward_1: EXP节点反向传播(phiᵢ) - 9 次写入
      EXP_backward_2: EXP节点反向传播(phiⱼ) - 9 次写入
      RV_backward: RV节点反向传播 - 3 次写入
      VP_backward_1: VP节点反向传播(tᵢ) - 0 次写入 (SIMD不需要写入)
      VP_backward_2: VP节点反向传播(tⱼ) - 0 次写入 (SIMD不需要写入)
      StaticVMM_config: config matrix StaticVMM - 36 次写入
      J^T*Ω计算: 144 次写入 (StaticVMM)
      Hessian块: (J^T*Ω)*J - 576 次写入 (StaticVMM)
      累加操作: H += block - 0 次写入 (SIMD不需要写入)

🎯 Hessian构建总结:
   每因子写入: 798
   因子数量: 21
   Hessian总写入: 16,758

📋 原语操作使用统计 (基于Table 3)
------------------------------------------------------------
   原语操作使用统计:
      Exp: 42 次 × 0 = 0 写入
      Log: 21 次 × 0 = 0 写入
      RT: 63 次 × 0 = 0 写入
      RR: 42 次 × 9 = 378 写入
      RV: 42 次 × 3 = 126 写入
      VP: 63 次 × 0 = 0 写入
      Jr: 42 次 × 9 = 378 写入

🎯 原语操作总写入: 882

🎯 修正后的总RRAM写入统计 (考虑SIMD特性):
   误差计算 (右侧b): 945
   Hessian构建 (左侧H): 16,758
   QR分解求解:
     - 预处理: 36
     - 分解: 72
     - 求解: 6
   系统求解总计: 114
   总计: 17,817


🎯 所有算法的误差公式和H矩阵结果汇总
================================================================================

🔍 robot - localization
--------------------------------------------------
📐 误差公式:
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)
   统一表示: 使用Θ操作计算实际观测与理想模型的偏差
   误差计算结果: 45 × 212 = 9,540 次写入

🔧 H矩阵构建 (MO-DFG反向求导):
   MO-DFG反向传播: 42 次写入
   StaticVMM配置: 18 次写入
   J^T*Ω计算: 72 次写入
   Hessian块: 144 次写入
   H矩阵结果: 276 × 212 = 58,512 次写入

   📊 算法总计: 68,082 次RRAM写入

🔍 robot - planning
--------------------------------------------------
📐 误差公式:
   路径误差: 基于⊕操作计算机器人链接点的世界坐标
   碰撞误差: 距离场计算
   平滑性误差: 路径点差值计算
   误差计算结果: 19 × 52 = 988 次写入

🔧 H矩阵构建 (MO-DFG反向求导):
   MO-DFG反向传播: 36 次写入
   DynamicVMM: 6 次写入
   J^T*Ω计算: 72 次写入
   Hessian块: 144 次写入
   H矩阵结果: 258 × 52 = 13,416 次写入

   📊 算法总计: 14,434 次RRAM写入

🔍 robot - control
--------------------------------------------------
📐 误差公式:
   状态误差: 实际状态与预测状态的偏差
   控制误差: 实际控制输入与理想输入的偏差
   动力学误差: 基于动力学模型的预测误差
   误差计算结果: 9 × 61 = 549 次写入

🔧 H矩阵构建 (MO-DFG反向求导):
   MO-DFG反向传播: 18 次写入
   DynamicVMM: 3 次写入
   J^T*Ω计算: 18 次写入
   Hessian块: 36 次写入
   H矩阵结果: 75 × 61 = 4,575 次写入

   📊 算法总计: 5,154 次RRAM写入

🔍 autovehicle - localization
--------------------------------------------------
📐 误差公式:
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)
   统一表示: 使用Θ操作计算实际观测与理想模型的偏差
   误差计算结果: 45 × 251 = 11,295 次写入

🔧 H矩阵构建 (MO-DFG反向求导):
   MO-DFG反向传播: 42 次写入
   StaticVMM配置: 18 次写入
   J^T*Ω计算: 72 次写入
   Hessian块: 144 次写入
   H矩阵结果: 276 × 251 = 69,276 次写入

   📊 算法总计: 80,601 次RRAM写入

🔍 quadrotor - localization
--------------------------------------------------
📐 误差公式:
   方向误差: eφ = Log(ΔRᵢⱼᵀ Rᵢᵀ Rⱼ)
   位置误差: ep = ΔRᵢⱼᵀ(Rᵢᵀ(tⱼ - tᵢ) - Δtᵢⱼ)
   统一表示: 使用Θ操作计算实际观测与理想模型的偏差
   误差计算结果: 45 × 21 = 945 次写入

🔧 H矩阵构建 (MO-DFG反向求导):
   MO-DFG反向传播: 42 次写入
   StaticVMM配置: 36 次写入
   J^T*Ω计算: 144 次写入
   Hessian块: 576 次写入
   H矩阵结果: 798 × 21 = 16,758 次写入

   📊 算法总计: 17,733 次RRAM写入

================================================================================
🎯 修正后总结
================================================================================
修正后总RRAM写入: 185,989
平均每算法: 37,197

关键修正:
1. 使用正确的Equation (4)公式
2. 反向遍历构建Hessian矩阵
3. 前向遍历计算误差向量
4. 基于Table 3的原语操作
5. SIMD特性: Exp/Log/RT/VP在SIMD中，只有乘法需要写入
6. QR分解多步骤: 预处理 + 分解 + 求解
7. MO-DFG反向求导: 基于具体的反向传播操作序列
8. Jr操作: Right Jacobian (右雅可比) 计算
