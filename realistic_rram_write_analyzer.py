#!/usr/bin/env python3
"""
Realistic RRAM Write Count Analyzer for 12 Factor Graph Algorithms

Based on Orianna compiler paper architecture and realistic problem scales.
Estimates RRAM write operations for the complete workflow:
1. Factor computation (Jacobian and error calculation)
2. Linear equation construction (coefficient matrix A and RHS vector b)
3. QR decomposition using Givens rotations
4. Back substitution for solving linear systems

The 12 algorithms analyzed:
- Robot: localization, planning, control
- Manipulator: localization, planning, control  
- Autonomous Vehicle: localization, planning, control
- Quadrotor: localization, planning, control
"""

import sys
import os
import numpy as np
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

@dataclass
class RRAMWriteAnalysis:
    """Analysis results for RRAM write operations"""
    app_name: str
    algorithm: str
    
    # Factor graph structure
    total_variables: int
    total_factors: int
    matrix_dimensions: Tuple[int, int]  # (constraints, variables)
    
    # RRAM write breakdown
    factor_computation_writes: int      # Jacobian + error computation
    matrix_construction_writes: int     # Building coefficient matrix A and vector b
    qr_decomposition_writes: int        # QR decomposition matrices
    back_substitution_writes: int       # Solving triangular system
    
    # Totals
    total_writes_per_iteration: int
    estimated_iterations: int           # Typical optimization iterations
    total_writes_complete_solve: int

class RealisticRRAMAnalyzer:
    """Analyzer for realistic RRAM write operations in factor graph SLAM"""
    
    def __init__(self):
        # Realistic problem scales based on research literature
        self.realistic_scales = {
            # Robot localization: typical indoor SLAM
            ('robot', 'localization'): {'poses': 50, 'landmarks': 30, 'iterations': 10},
            ('robot', 'planning'): {'waypoints': 30, 'obstacles': 20, 'iterations': 15},
            ('robot', 'control'): {'time_steps': 20, 'constraints': 15, 'iterations': 5},
            
            # Manipulator: 6-DOF arm with workspace constraints
            ('manipulator', 'localization'): {'joints': 6, 'measurements': 20, 'iterations': 8},
            ('manipulator', 'planning'): {'configurations': 25, 'constraints': 30, 'iterations': 20},
            ('manipulator', 'control'): {'time_steps': 15, 'joint_limits': 6, 'iterations': 8},
            
            # Autonomous vehicle: urban driving scenario
            ('autovehicle', 'localization'): {'poses': 100, 'landmarks': 50, 'iterations': 12},
            ('autovehicle', 'planning'): {'path_points': 40, 'traffic_constraints': 25, 'iterations': 25},
            ('autovehicle', 'control'): {'time_steps': 25, 'safety_constraints': 20, 'iterations': 10},
            
            # Quadrotor: 3D flight with visual-inertial odometry
            ('quadrotor', 'localization'): {'poses': 80, 'features': 60, 'imu_factors': 40, 'iterations': 15},
            ('quadrotor', 'planning'): {'waypoints': 35, 'obstacles': 30, 'dynamics': 25, 'iterations': 30},
            ('quadrotor', 'control'): {'time_steps': 30, 'attitude_constraints': 25, 'iterations': 12},
        }
        
        # RRAM write costs per operation (based on hardware analysis)
        self.write_costs = {
            'jacobian_element': 1,          # Write single Jacobian element
            'error_element': 1,             # Write single error element  
            'matrix_element': 1,            # Write matrix coefficient
            'qr_givens_rotation': 4,        # Givens rotation updates 4 elements
            'triangular_solve_element': 1,  # Back substitution element
        }
    
    def analyze_algorithm(self, app_name: str, algorithm: str) -> RRAMWriteAnalysis:
        """Analyze RRAM writes for a specific algorithm"""
        
        print(f"\n🔍 Analyzing {app_name} - {algorithm}")
        
        # Get factor graph structure
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        # Get realistic scale parameters
        scale_params = self.realistic_scales.get((app_name, algorithm), {})
        
        # Calculate matrix dimensions
        total_vars = sum(len(var_list) for var_list in nodes.values())
        total_factors = len(factors)
        
        # Estimate coefficient matrix dimensions (constraints x variables)
        # Each factor typically contributes 2-3 constraints
        constraints = total_factors * 2.5  # Average constraint contribution
        variables = total_vars * 2         # Assuming 2D variables on average
        
        matrix_dims = (int(constraints), int(variables))
        
        print(f"  Factor graph: {total_vars} variables, {total_factors} factors")
        print(f"  Matrix dimensions: {matrix_dims[0]} x {matrix_dims[1]}")
        
        # Calculate RRAM writes for each stage
        factor_writes = self._calculate_factor_computation_writes(factors, nodes)
        matrix_writes = self._calculate_matrix_construction_writes(matrix_dims)
        qr_writes = self._calculate_qr_decomposition_writes(matrix_dims)
        solve_writes = self._calculate_back_substitution_writes(matrix_dims)
        
        # Total per iteration
        total_per_iter = factor_writes + matrix_writes + qr_writes + solve_writes
        
        # Estimate typical iterations for convergence
        iterations = scale_params.get('iterations', 10)
        total_complete = total_per_iter * iterations
        
        print(f"  Factor computation: {factor_writes:,} writes")
        print(f"  Matrix construction: {matrix_writes:,} writes") 
        print(f"  QR decomposition: {qr_writes:,} writes")
        print(f"  Back substitution: {solve_writes:,} writes")
        print(f"  Per iteration: {total_per_iter:,} writes")
        print(f"  Complete solve ({iterations} iter): {total_complete:,} writes")
        
        return RRAMWriteAnalysis(
            app_name=app_name,
            algorithm=algorithm,
            total_variables=total_vars,
            total_factors=total_factors,
            matrix_dimensions=matrix_dims,
            factor_computation_writes=factor_writes,
            matrix_construction_writes=matrix_writes,
            qr_decomposition_writes=qr_writes,
            back_substitution_writes=solve_writes,
            total_writes_per_iteration=total_per_iter,
            estimated_iterations=iterations,
            total_writes_complete_solve=total_complete
        )
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """Get factor graph structure for application"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"Unknown application: {app_name} - {algorithm}")
            
        return generator()
    
    def _calculate_factor_computation_writes(self, factors: Dict, nodes: Dict) -> int:
        """Calculate RRAM writes for factor computation (Jacobian + error)"""
        
        total_writes = 0
        
        for factor_name, connected_vars in factors.items():
            # Estimate Jacobian size: each variable contributes ~2-3 dimensions
            var_dims = len(connected_vars) * 2.5  # Average variable dimension
            jacobian_elements = int(var_dims * var_dims)  # Square Jacobian block
            
            # Error vector elements
            error_elements = int(var_dims)
            
            # RRAM writes
            jacobian_writes = jacobian_elements * self.write_costs['jacobian_element']
            error_writes = error_elements * self.write_costs['error_element']
            
            total_writes += jacobian_writes + error_writes
        
        return total_writes
    
    def _calculate_matrix_construction_writes(self, matrix_dims: Tuple[int, int]) -> int:
        """Calculate RRAM writes for constructing coefficient matrix A and vector b"""
        
        m, n = matrix_dims
        
        # Coefficient matrix A (sparse, ~10% non-zero)
        matrix_elements = int(m * n * 0.1)  # Sparse structure
        matrix_writes = matrix_elements * self.write_costs['matrix_element']
        
        # RHS vector b
        vector_writes = m * self.write_costs['matrix_element']
        
        return matrix_writes + vector_writes
    
    def _calculate_qr_decomposition_writes(self, matrix_dims: Tuple[int, int]) -> int:
        """Calculate RRAM writes for QR decomposition using Givens rotations"""
        
        m, n = matrix_dims
        
        # Givens rotations: approximately (m-n)*n rotations needed
        if m > n:
            num_rotations = (m - n) * n
        else:
            num_rotations = m * n // 2  # Square case approximation
        
        # Each Givens rotation updates multiple matrix elements
        rotation_writes = num_rotations * self.write_costs['qr_givens_rotation']
        
        # Write final Q and R matrices
        q_writes = m * min(m, n) * self.write_costs['matrix_element']
        r_writes = min(m, n) * n * self.write_costs['matrix_element']
        
        return rotation_writes + q_writes + r_writes
    
    def _calculate_back_substitution_writes(self, matrix_dims: Tuple[int, int]) -> int:
        """Calculate RRAM writes for back substitution"""
        
        m, n = matrix_dims
        solve_dim = min(m, n)
        
        # Triangular solve: approximately n^2/2 operations
        solve_operations = solve_dim * solve_dim // 2
        solve_writes = solve_operations * self.write_costs['triangular_solve_element']
        
        # Write solution vector
        solution_writes = solve_dim * self.write_costs['matrix_element']
        
        return solve_writes + solution_writes
    
    def analyze_all_algorithms(self) -> List[RRAMWriteAnalysis]:
        """Analyze all 12 algorithms"""
        
        algorithms = [
            ('robot', 'localization'),
            ('robot', 'planning'), 
            ('robot', 'control'),
            ('manipulator', 'localization'),
            ('manipulator', 'planning'),
            ('manipulator', 'control'),
            ('autovehicle', 'localization'),
            ('autovehicle', 'planning'),
            ('autovehicle', 'control'),
            ('quadrotor', 'localization'),
            ('quadrotor', 'planning'),
            ('quadrotor', 'control'),
        ]
        
        print("🚀 Realistic RRAM Write Analysis for 12 Factor Graph Algorithms")
        print("=" * 80)
        print("Based on Orianna compiler architecture and realistic problem scales")
        
        results = []
        
        for app_name, algorithm in algorithms:
            try:
                analysis = self.analyze_algorithm(app_name, algorithm)
                results.append(analysis)
            except Exception as e:
                print(f"❌ Error analyzing {app_name} - {algorithm}: {e}")
        
        return results
    
    def print_summary_table(self, results: List[RRAMWriteAnalysis]):
        """Print summary table of all results"""
        
        print(f"\n{'='*100}")
        print("📊 RRAM WRITE COUNT SUMMARY - 12 FACTOR GRAPH ALGORITHMS")
        print(f"{'='*100}")
        
        print(f"{'Algorithm':<25} {'Matrix Size':<15} {'Per Iter':<12} {'Iterations':<10} {'Total Writes':<15}")
        print("-" * 100)
        
        total_all_writes = 0
        
        for result in results:
            app_full = f"{result.app_name}_{result.algorithm}"
            matrix_size = f"{result.matrix_dimensions[0]}x{result.matrix_dimensions[1]}"
            
            print(f"{app_full:<25} {matrix_size:<15} {result.total_writes_per_iteration:<12,} "
                  f"{result.estimated_iterations:<10} {result.total_writes_complete_solve:<15,}")
            
            total_all_writes += result.total_writes_complete_solve
        
        print("-" * 100)
        print(f"{'TOTAL ACROSS ALL ALGORITHMS':<25} {'':<15} {'':<12} {'':<10} {total_all_writes:<15,}")
        print(f"{'AVERAGE PER ALGORITHM':<25} {'':<15} {'':<12} {'':<10} {total_all_writes//len(results):<15,}")


def main():
    """Main execution"""
    
    analyzer = RealisticRRAMAnalyzer()
    results = analyzer.analyze_all_algorithms()
    analyzer.print_summary_table(results)
    
    # Save detailed results
    print(f"\n💾 Analysis complete. {len(results)} algorithms analyzed.")
    return results


if __name__ == "__main__":
    results = main()
