#!/usr/bin/env python3
"""
详细分析12个算法的RRAM写入内容和计算过程确定方法

解释：
1. 每个算法具体写入什么数据
2. 如何确定完整的计算过程
3. 基于slam_factorgraph.py的实际操作映射
4. 从因子图结构到硬件操作的完整链路
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

class DetailedWriteAnalyzer:
    """详细分析12个算法的写入内容"""
    
    def __init__(self):
        self.slam_operations_mapping = {
            # 基于slam_factorgraph.py的实际操作序列
            'input_storage': [
                ('deltaRij', (3, 3), 'Store', '旋转增量矩阵'),
                ('deltaTij', (3, 1), 'Store', '平移增量向量'),
                ('ti', (3, 1), 'Store', '位姿i的平移部分'),
                ('tj', (3, 1), 'Store', '位姿j的平移部分'),
                ('phii', (3, 1), 'Store', '位姿i的旋转向量'),
                ('phij', (3, 1), 'Store', '位姿j的旋转向量'),
            ],
            'weight_configuration': [
                ('skew_config_matrix', (9, 3), 'WriteWeights', '反对称矩阵配置权重'),
                ('omega_matrix', (6, 6), 'WriteWeights', '信息矩阵Ω权重'),
            ],
            'static_vmm_computations': [
                ('skew_matrix', (3, 3), 'StaticVMM', '反对称矩阵[ti-tj]^'),
                ('weighted_error', (6, 1), 'StaticVMM', '加权误差向量b=-Ω*[eo;ep]'),
                ('weighted_jacobian', (6, 12), 'StaticVMM', '加权Jacobian矩阵A=-Ω*J'),
            ],
            'qr_factorization': [
                ('givens_parameters', (2, 1), 'Factorization', 'Givens旋转参数(c,s)'),
            ],
            'simd_operations': [
                ('exp_mapping', 'Expmapping', '指数映射φ→R'),
                ('log_mapping', 'LOG', '对数映射R→φ'),
                ('matrix_transpose', 'RT', '矩阵转置R^T'),
                ('dynamic_vmm', 'DynamicVMM', '动态矩阵乘法'),
                ('vector_operations', 'VP', '向量加减运算'),
                ('matrix_vector_mult', 'RV', '矩阵向量乘法'),
                ('preprocessing', 'Preprocessing', 'QR预处理计算'),
                ('transpose', 'Transpose', '矩阵转置操作'),
            ]
        }
    
    def analyze_algorithm_write_content(self, app_name: str, algorithm: str):
        """分析单个算法的具体写入内容"""
        
        print(f"\n🔍 详细分析 {app_name} - {algorithm} 的写入内容")
        print("=" * 80)
        
        # 获取因子图结构
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        print(f"📊 因子图结构分析:")
        print(f"   变量节点: {nodes}")
        print(f"   因子数量: {len(factors)}")
        print(f"   因子类型: {list(factors.keys())[:5]}..." if len(factors) > 5 else f"   因子: {list(factors.keys())}")
        print()
        
        # 分析每种操作的具体写入内容
        self._analyze_input_storage_writes(nodes, factors)
        self._analyze_weight_configuration_writes(nodes, factors)
        self._analyze_static_vmm_writes(nodes, factors)
        self._analyze_factorization_writes(nodes, factors)
        self._analyze_simd_operations(nodes, factors)
        
        # 分析计算过程确定方法
        self._explain_computation_process_determination(app_name, algorithm, nodes, factors)
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()
    
    def _analyze_input_storage_writes(self, nodes: Dict, factors: Dict):
        """分析输入存储写入"""
        
        print("📦 阶段1: 输入数据存储 (Store操作)")
        print("-" * 50)
        print("基于slam_factorgraph.py第42-107行的Store操作")
        print()
        
        for name, dims, op_type, description in self.slam_operations_mapping['input_storage']:
            elements = dims[0] * dims[1]
            print(f"   {name}: {dims[0]}×{dims[1]} = {elements} 个浮点数")
            print(f"      操作类型: {op_type}")
            print(f"      数据内容: {description}")
            print(f"      RRAM写入: {elements} 次 (每个32位浮点数)")
            print()
        
        # 根据因子数量缩放
        factor_count = len(factors)
        scale_factor = factor_count / 10  # 基准10个因子
        total_base = sum(dims[0] * dims[1] for _, dims, _, _ in self.slam_operations_mapping['input_storage'])
        scaled_total = int(total_base * scale_factor)
        
        print(f"   基础写入: {total_base} 次")
        print(f"   因子缩放: ×{scale_factor:.2f} (基于{factor_count}个因子)")
        print(f"   实际写入: {scaled_total} 次")
        print()
    
    def _analyze_weight_configuration_writes(self, nodes: Dict, factors: Dict):
        """分析权重配置写入"""
        
        print("⚖️  阶段2: 权重配置 (WriteWeights操作)")
        print("-" * 50)
        print("基于slam_factorgraph.py第319行和第417行的WriteWeights操作")
        print()
        
        for name, dims, op_type, description in self.slam_operations_mapping['weight_configuration']:
            elements = dims[0] * dims[1]
            print(f"   {name}: {dims[0]}×{dims[1]} = {elements} 个权重值")
            print(f"      操作类型: {op_type}")
            print(f"      数据内容: {description}")
            print(f"      RRAM写入: {elements} 次 (配置RRAM权重)")
            print()
        
        factor_count = len(factors)
        scale_factor = factor_count / 10
        total_base = sum(dims[0] * dims[1] for _, dims, _, _ in self.slam_operations_mapping['weight_configuration'])
        scaled_total = int(total_base * scale_factor)
        
        print(f"   基础写入: {total_base} 次")
        print(f"   因子缩放: ×{scale_factor:.2f}")
        print(f"   实际写入: {scaled_total} 次")
        print()
    
    def _analyze_static_vmm_writes(self, nodes: Dict, factors: Dict):
        """分析静态VMM写入"""
        
        print("🔢 阶段3: 静态VMM计算 (StaticVMM操作)")
        print("-" * 50)
        print("基于slam_factorgraph.py第333行、第429行、第443行的StaticVMM操作")
        print()
        
        for name, dims, op_type, description in self.slam_operations_mapping['static_vmm_computations']:
            elements = dims[0] * dims[1]
            print(f"   {name}: {dims[0]}×{dims[1]} = {elements} 个计算结果")
            print(f"      操作类型: {op_type}")
            print(f"      数据内容: {description}")
            print(f"      RRAM写入: {elements} 次 (存储VMM计算结果)")
            print()
        
        factor_count = len(factors)
        scale_factor = factor_count / 10
        total_base = sum(dims[0] * dims[1] for _, dims, _, _ in self.slam_operations_mapping['static_vmm_computations'])
        scaled_total = int(total_base * scale_factor)
        
        print(f"   基础写入: {total_base} 次")
        print(f"   因子缩放: ×{scale_factor:.2f}")
        print(f"   实际写入: {scaled_total} 次")
        print()
    
    def _analyze_factorization_writes(self, nodes: Dict, factors: Dict):
        """分析分解操作写入"""
        
        print("🔄 阶段4: QR分解 (Factorization操作)")
        print("-" * 50)
        print("基于slam_factorgraph.py第577行的Factorization操作")
        print()
        
        # 估算Givens旋转次数
        N, M = 6, 12  # 基于slam_factorgraph.py的参数
        total_rotations = 0
        for col in range(min(N-1, M)):
            for target_row in range(col + 1, N):
                total_rotations += 1
        
        print(f"   Givens旋转次数: {total_rotations}")
        print(f"   每次旋转参数: 2个 (cos θ, sin θ)")
        print(f"   参数总数: {total_rotations * 2}")
        print(f"   操作类型: Factorization")
        print(f"   数据内容: QR分解的Givens旋转参数")
        print(f"   RRAM写入: {total_rotations * 2} 次")
        print()
        
        # 根据问题规模缩放
        total_vars = sum(len(var_list) for var_list in nodes.values())
        scale_factor = total_vars / 20  # 基准20个变量
        scaled_writes = int(total_rotations * 2 * scale_factor)
        
        print(f"   基础写入: {total_rotations * 2} 次")
        print(f"   变量缩放: ×{scale_factor:.2f} (基于{total_vars}个变量)")
        print(f"   实际写入: {scaled_writes} 次")
        print()
    
    def _analyze_simd_operations(self, nodes: Dict, factors: Dict):
        """分析SIMD操作 (不写RRAM)"""
        
        print("💻 SIMD计算操作 (无RRAM写入)")
        print("-" * 50)
        print("基于slam_factorgraph.py中的各种SIMD操作")
        print()
        
        for name, op_type, description in self.slam_operations_mapping['simd_operations']:
            print(f"   {name}: {op_type}")
            print(f"      数据内容: {description}")
            print(f"      执行位置: SIMD处理单元")
            print(f"      RRAM写入: 0次 (结果直接用于下一步计算)")
            print()
        
        # 估算SIMD操作总数
        factor_count = len(factors)
        estimated_simd_ops = len(self.slam_operations_mapping['simd_operations']) * factor_count * 2
        
        print(f"   估算SIMD操作总数: {estimated_simd_ops}")
        print(f"   计算效率: 大部分计算在SIMD上完成，减少RRAM访问")
        print()
    
    def _explain_computation_process_determination(self, app_name: str, algorithm: str, 
                                                 nodes: Dict, factors: Dict):
        """解释计算过程的确定方法"""
        
        print("🔧 计算过程确定方法")
        print("=" * 80)
        
        print("📋 1. 因子图结构分析:")
        print(f"   - 从application.py获取{app_name}_{algorithm}的因子图定义")
        print(f"   - 变量类型: {list(nodes.keys())}")
        print(f"   - 因子连接: 每个因子连接的变量数量和类型")
        print(f"   - 约束类型: 先验、里程计、测量、动力学等")
        print()
        
        print("📋 2. 操作序列映射:")
        print("   - 基于slam_factorgraph.py的实际操作序列")
        print("   - 每个因子对应的计算步骤:")
        print("     * 误差计算: e = h(x) - z")
        print("     * Jacobian计算: J = ∂h/∂x")
        print("     * 信息矩阵: H = J^T Ω J")
        print("     * 信息向量: b = J^T Ω e")
        print("   - QR分解: Givens旋转序列")
        print("   - 回代求解: 三角系统求解")
        print()
        
        print("📋 3. 硬件操作分类:")
        print("   - Store操作: 输入数据存储到RRAM")
        print("   - WriteWeights操作: 权重配置到RRAM")
        print("   - StaticVMM操作: 使用预存权重的矩阵乘法")
        print("   - DynamicVMM操作: SIMD上的动态矩阵乘法")
        print("   - Factorization操作: QR分解参数存储")
        print("   - 其他操作: SIMD上的向量和矩阵运算")
        print()
        
        print("📋 4. 缩放因子确定:")
        print(f"   - 因子数量缩放: {len(factors)}/10 = {len(factors)/10:.2f}")
        print(f"   - 变量数量缩放: {sum(len(v) for v in nodes.values())}/20 = {sum(len(v) for v in nodes.values())/20:.2f}")
        print("   - 基于slam_factorgraph.py的基准参数")
        print("   - 考虑不同算法的复杂度差异")
        print()
        
        print("📋 5. 验证方法:")
        print("   - 与slam_factorgraph.py的操作序列对比")
        print("   - 检查操作类型的RRAM写入需求")
        print("   - 验证数据维度的一致性")
        print("   - 确保硬件约束的满足")


def main():
    """主函数 - 分析几个代表性算法"""
    
    analyzer = DetailedWriteAnalyzer()
    
    # 分析几个代表性算法
    representative_algorithms = [
        ('robot', 'localization'),      # 最复杂的定位算法
        ('autovehicle', 'localization'), # 最大规模的算法
        ('manipulator', 'planning'),     # 中等复杂度规划算法
        ('quadrotor', 'control'),        # 简单控制算法
    ]
    
    print("🔍 12个算法的详细写入内容和计算过程分析")
    print("=" * 80)
    print("基于slam_factorgraph.py的实际操作流程")
    
    for app_name, algorithm in representative_algorithms:
        try:
            analyzer.analyze_algorithm_write_content(app_name, algorithm)
        except Exception as e:
            print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")
    
    print("\n🎯 总结")
    print("=" * 80)
    print("1. 每个算法的写入内容基于slam_factorgraph.py的实际操作序列")
    print("2. 计算过程通过因子图结构→操作序列→硬件操作的映射确定")
    print("3. RRAM写入只发生在Store、WriteWeights、StaticVMM、Factorization操作")
    print("4. 大部分计算在SIMD上完成，显著减少RRAM写入需求")
    print("5. 缩放因子基于实际因子图规模和slam_factorgraph.py的基准参数")


if __name__ == "__main__":
    main()
