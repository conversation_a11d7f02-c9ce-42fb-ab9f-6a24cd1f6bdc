#!/usr/bin/env python3
"""
RRAM Data Type Analysis for Factor Graph Applications

This tool provides detailed analysis of what specific data is written to RRAM
and identifies optimization opportunities to reduce or eliminate write operations.

Key Analysis:
1. Data type classification (weights, intermediate results, temporary matrices)
2. Write frequency and reuse patterns
3. Optimization opportunities (caching, reuse, computation alternatives)
4. Critical vs non-critical write operations

Author: Event-Driven-Simulator Team
"""

import sys
import os
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict, Counter
from dataclasses import dataclass
from enum import Enum

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from factorgraph.application.application import *
    print("Successfully imported factor graph applications.")
except ImportError as e:
    print(f"Error importing modules: {e}")
    raise

class DataType(Enum):
    """Classification of data types written to RRAM"""
    STATIC_WEIGHTS = "static_weights"           # Fixed transformation matrices
    DYNAMIC_WEIGHTS = "dynamic_weights"         # Updated weight matrices
    JACOBIAN_MATRIX = "jacobian_matrix"         # Jacobian matrices for linearization
    ERROR_VECTOR = "error_vector"               # Error/residual vectors
    QR_MATRICES = "qr_matrices"                 # Q and R matrices from decomposition
    INTERMEDIATE_RESULT = "intermediate_result"  # Temporary computation results
    GIVENS_ROTATION = "givens_rotation"         # Givens rotation matrices
    COEFFICIENT_MATRIX = "coefficient_matrix"   # System coefficient matrices
    RHS_VECTOR = "rhs_vector"                   # Right-hand side vectors

class WriteNecessity(Enum):
    """Classification of write operation necessity"""
    ESSENTIAL = "essential"           # Cannot be avoided
    OPTIMIZABLE = "optimizable"       # Can be reduced through optimization
    ELIMINABLE = "eliminable"         # Can be completely eliminated
    CACHEABLE = "cacheable"           # Can be cached and reused

@dataclass
class RRAMDataWrite:
    """Detailed information about a specific RRAM data write"""
    data_name: str
    data_type: DataType
    write_necessity: WriteNecessity
    matrix_size: Tuple[int, int]
    write_frequency: int              # How often this data is written
    reuse_potential: int              # How many times it could be reused
    computation_cost: float           # Cost to recompute vs store
    optimization_notes: str           # Specific optimization suggestions

@dataclass
class OptimizationOpportunity:
    """Identified optimization opportunity"""
    opportunity_type: str
    affected_data_types: List[DataType]
    potential_reduction: float        # Percentage reduction in writes
    implementation_complexity: str    # Low/Medium/High
    description: str

class RRAMDataAnalyzer:
    """
    Analyzes RRAM data writes and identifies optimization opportunities
    """
    
    def __init__(self):
        self.data_writes: List[RRAMDataWrite] = []
        self.optimization_opportunities: List[OptimizationOpportunity] = []
        
        # Data type characteristics
        self.data_characteristics = {
            DataType.STATIC_WEIGHTS: {
                'recompute_cost': 'low',
                'change_frequency': 'never',
                'size_variability': 'fixed',
                'optimization_potential': 'high'
            },
            DataType.DYNAMIC_WEIGHTS: {
                'recompute_cost': 'medium',
                'change_frequency': 'every_iteration',
                'size_variability': 'fixed',
                'optimization_potential': 'medium'
            },
            DataType.JACOBIAN_MATRIX: {
                'recompute_cost': 'high',
                'change_frequency': 'every_iteration',
                'size_variability': 'variable',
                'optimization_potential': 'low'
            },
            DataType.ERROR_VECTOR: {
                'recompute_cost': 'medium',
                'change_frequency': 'every_iteration',
                'size_variability': 'variable',
                'optimization_potential': 'medium'
            },
            DataType.QR_MATRICES: {
                'recompute_cost': 'very_high',
                'change_frequency': 'every_elimination',
                'size_variability': 'variable',
                'optimization_potential': 'low'
            },
            DataType.INTERMEDIATE_RESULT: {
                'recompute_cost': 'low',
                'change_frequency': 'every_operation',
                'size_variability': 'variable',
                'optimization_potential': 'very_high'
            },
            DataType.GIVENS_ROTATION: {
                'recompute_cost': 'low',
                'change_frequency': 'every_qr_step',
                'size_variability': 'fixed',
                'optimization_potential': 'high'
            }
        }
    
    def analyze_factor_graph_data_writes(self, app_name: str, algorithm: str) -> Dict[str, Any]:
        """
        Analyze data writes for a factor graph application
        
        Returns:
            Dictionary with detailed analysis results
        """
        print(f"\n🔍 Analyzing RRAM data writes for {app_name} - {algorithm}")
        
        # Get factor graph structure
        nodes, factors, layout = self._get_factor_graph(app_name, algorithm)
        
        # Analyze each type of data write
        self._analyze_factor_computation_writes(factors, nodes)
        self._analyze_variable_elimination_writes(nodes, factors)
        
        # Identify optimization opportunities
        self._identify_optimization_opportunities()
        
        # Generate analysis report
        return self._generate_analysis_report(app_name, algorithm)
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """Get factor graph structure from application generators"""
        
        generator_map = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator_func = generator_map.get((app_name, algorithm))
        if not generator_func:
            raise ValueError(f"Unknown application: {app_name} - {algorithm}")
        
        return generator_func()
    
    def _analyze_factor_computation_writes(self, factors: Dict[str, List[str]], 
                                         nodes: Dict[str, List[str]]):
        """Analyze data writes for factor computations"""
        
        print(f"  📊 Analyzing factor computation writes...")
        
        for factor_name, connected_vars in factors.items():
            factor_type = self._classify_factor_type(factor_name)
            
            # Jacobian matrix writes
            jacobian_size = self._estimate_jacobian_size(connected_vars, nodes)
            jacobian_write = RRAMDataWrite(
                data_name=f"{factor_name}_jacobian",
                data_type=DataType.JACOBIAN_MATRIX,
                write_necessity=WriteNecessity.ESSENTIAL,
                matrix_size=jacobian_size,
                write_frequency=1,  # Once per factor evaluation
                reuse_potential=0,  # Jacobians change every iteration
                computation_cost=1.0,  # High cost to recompute
                optimization_notes="Cannot be eliminated, but can use sparse storage"
            )
            self.data_writes.append(jacobian_write)
            
            # Error vector writes
            error_size = (len(connected_vars) * 3, 1)  # Approximate error vector size
            error_write = RRAMDataWrite(
                data_name=f"{factor_name}_error",
                data_type=DataType.ERROR_VECTOR,
                write_necessity=WriteNecessity.OPTIMIZABLE,
                matrix_size=error_size,
                write_frequency=1,
                reuse_potential=0,
                computation_cost=0.3,  # Medium cost to recompute
                optimization_notes="Can use streaming computation to avoid storage"
            )
            self.data_writes.append(error_write)
            
            # Static transformation matrices (if applicable)
            if factor_type in ['prior', 'measurement']:
                transform_size = (3, 3)  # Typical transformation matrix
                transform_write = RRAMDataWrite(
                    data_name=f"{factor_name}_transform",
                    data_type=DataType.STATIC_WEIGHTS,
                    write_necessity=WriteNecessity.ELIMINABLE,
                    matrix_size=transform_size,
                    write_frequency=1,
                    reuse_potential=100,  # Can be reused many times
                    computation_cost=0.1,  # Low cost to recompute
                    optimization_notes="Can be computed on-the-fly or cached in SRAM"
                )
                self.data_writes.append(transform_write)
    
    def _analyze_variable_elimination_writes(self, nodes: Dict[str, List[str]], 
                                           factors: Dict[str, List[str]]):
        """Analyze data writes for variable elimination"""
        
        print(f"  🔄 Analyzing variable elimination writes...")
        
        all_variables = []
        for var_type, var_list in nodes.items():
            for var in var_list:
                all_variables.append((var, var_type))
        
        for var_name, var_type in all_variables:
            connected_factors = [f for f, vars in factors.items() if var_name in vars]
            
            # QR decomposition matrices
            qr_size = self._estimate_qr_size(var_type, len(connected_factors))
            
            # Q matrix write
            q_write = RRAMDataWrite(
                data_name=f"{var_name}_Q_matrix",
                data_type=DataType.QR_MATRICES,
                write_necessity=WriteNecessity.ESSENTIAL,
                matrix_size=(qr_size[0], qr_size[0]),
                write_frequency=1,
                reuse_potential=0,
                computation_cost=1.0,
                optimization_notes="Essential for elimination, but can use Givens rotations to reduce storage"
            )
            self.data_writes.append(q_write)
            
            # R matrix write
            r_write = RRAMDataWrite(
                data_name=f"{var_name}_R_matrix",
                data_type=DataType.QR_MATRICES,
                write_necessity=WriteNecessity.ESSENTIAL,
                matrix_size=qr_size,
                write_frequency=1,
                reuse_potential=1,  # R matrix is used in back-substitution
                computation_cost=1.0,
                optimization_notes="Essential, but can use upper triangular storage"
            )
            self.data_writes.append(r_write)
            
            # Givens rotation matrices (for incremental QR)
            num_givens = qr_size[0] * (qr_size[0] - 1) // 2
            for i in range(min(num_givens, 10)):  # Limit for analysis
                givens_write = RRAMDataWrite(
                    data_name=f"{var_name}_givens_{i}",
                    data_type=DataType.GIVENS_ROTATION,
                    write_necessity=WriteNecessity.ELIMINABLE,
                    matrix_size=(2, 2),
                    write_frequency=1,
                    reuse_potential=0,
                    computation_cost=0.05,  # Very low cost
                    optimization_notes="Can be computed on-the-fly, only 2 parameters needed"
                )
                self.data_writes.append(givens_write)

    def _identify_optimization_opportunities(self):
        """Identify specific optimization opportunities"""

        print(f"  💡 Identifying optimization opportunities...")

        # Opportunity 1: Eliminate static weight writes
        static_writes = [w for w in self.data_writes if w.data_type == DataType.STATIC_WEIGHTS]
        if static_writes:
            total_static_cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in static_writes)
            self.optimization_opportunities.append(OptimizationOpportunity(
                opportunity_type="Eliminate Static Weight Storage",
                affected_data_types=[DataType.STATIC_WEIGHTS],
                potential_reduction=100.0,  # Can eliminate completely
                implementation_complexity="Low",
                description=f"Replace {len(static_writes)} static weight matrices ({total_static_cells} cells) with on-the-fly computation"
            ))

        # Opportunity 2: Optimize Givens rotations
        givens_writes = [w for w in self.data_writes if w.data_type == DataType.GIVENS_ROTATION]
        if givens_writes:
            total_givens_cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in givens_writes)
            self.optimization_opportunities.append(OptimizationOpportunity(
                opportunity_type="On-the-fly Givens Computation",
                affected_data_types=[DataType.GIVENS_ROTATION],
                potential_reduction=100.0,
                implementation_complexity="Low",
                description=f"Compute {len(givens_writes)} Givens rotations on-demand using only cos/sin values"
            ))

        # Opportunity 3: Streaming error computation
        error_writes = [w for w in self.data_writes if w.data_type == DataType.ERROR_VECTOR]
        if error_writes:
            total_error_cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in error_writes)
            self.optimization_opportunities.append(OptimizationOpportunity(
                opportunity_type="Streaming Error Computation",
                affected_data_types=[DataType.ERROR_VECTOR],
                potential_reduction=80.0,  # Can reduce significantly
                implementation_complexity="Medium",
                description=f"Use streaming computation for {len(error_writes)} error vectors to reduce storage"
            ))

        # Opportunity 4: Sparse Jacobian storage
        jacobian_writes = [w for w in self.data_writes if w.data_type == DataType.JACOBIAN_MATRIX]
        if jacobian_writes:
            self.optimization_opportunities.append(OptimizationOpportunity(
                opportunity_type="Sparse Jacobian Storage",
                affected_data_types=[DataType.JACOBIAN_MATRIX],
                potential_reduction=60.0,  # Typical sparsity in SLAM
                implementation_complexity="High",
                description=f"Use sparse storage for {len(jacobian_writes)} Jacobian matrices (typical 60% sparsity)"
            ))

    def _generate_analysis_report(self, app_name: str, algorithm: str) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""

        # Calculate statistics
        total_writes = len(self.data_writes)
        total_cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in self.data_writes)

        # Group by data type
        writes_by_type = defaultdict(list)
        for write in self.data_writes:
            writes_by_type[write.data_type].append(write)

        # Group by necessity
        writes_by_necessity = defaultdict(list)
        for write in self.data_writes:
            writes_by_necessity[write.write_necessity].append(write)

        # Calculate optimization potential
        total_optimizable_cells = sum(
            w.matrix_size[0] * w.matrix_size[1]
            for w in self.data_writes
            if w.write_necessity in [WriteNecessity.OPTIMIZABLE, WriteNecessity.ELIMINABLE]
        )

        optimization_percentage = (total_optimizable_cells / total_cells * 100) if total_cells > 0 else 0

        return {
            'app_name': app_name,
            'algorithm': algorithm,
            'total_writes': total_writes,
            'total_cells': total_cells,
            'writes_by_type': dict(writes_by_type),
            'writes_by_necessity': dict(writes_by_necessity),
            'optimization_opportunities': self.optimization_opportunities,
            'optimization_percentage': optimization_percentage,
            'detailed_writes': self.data_writes
        }

    def print_detailed_analysis(self, analysis_result: Dict[str, Any]):
        """Print detailed analysis results"""

        print(f"\n{'='*80}")
        print(f"RRAM DATA WRITE ANALYSIS: {analysis_result['app_name'].upper()} - {analysis_result['algorithm'].upper()}")
        print(f"{'='*80}")

        print(f"\n📊 OVERALL STATISTICS:")
        print(f"  Total Write Operations:     {analysis_result['total_writes']:,}")
        print(f"  Total RRAM Cells Written:   {analysis_result['total_cells']:,}")
        print(f"  Optimization Potential:     {analysis_result['optimization_percentage']:.1f}%")

        print(f"\n🗂️  DATA TYPE BREAKDOWN:")
        for data_type, writes in analysis_result['writes_by_type'].items():
            cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in writes)
            percentage = (cells / analysis_result['total_cells'] * 100) if analysis_result['total_cells'] > 0 else 0
            print(f"  {data_type.value:<20}: {len(writes):3d} writes, {cells:6,} cells ({percentage:5.1f}%)")

        print(f"\n🎯 WRITE NECESSITY BREAKDOWN:")
        for necessity, writes in analysis_result['writes_by_necessity'].items():
            cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in writes)
            percentage = (cells / analysis_result['total_cells'] * 100) if analysis_result['total_cells'] > 0 else 0
            print(f"  {necessity.value:<15}: {len(writes):3d} writes, {cells:6,} cells ({percentage:5.1f}%)")

        print(f"\n💡 OPTIMIZATION OPPORTUNITIES:")
        total_reduction = 0
        for i, opp in enumerate(analysis_result['optimization_opportunities'], 1):
            print(f"  {i}. {opp.opportunity_type}")
            print(f"     Potential Reduction: {opp.potential_reduction:.1f}%")
            print(f"     Complexity: {opp.implementation_complexity}")
            print(f"     Description: {opp.description}")
            print()
            total_reduction += opp.potential_reduction

        if analysis_result['optimization_opportunities']:
            avg_reduction = total_reduction / len(analysis_result['optimization_opportunities'])
            print(f"  💰 POTENTIAL SAVINGS: {avg_reduction:.1f}% average reduction across optimizations")

        print(f"\n🔍 DETAILED WRITE ANALYSIS:")
        essential_writes = [w for w in analysis_result['detailed_writes'] if w.write_necessity == WriteNecessity.ESSENTIAL]
        eliminable_writes = [w for w in analysis_result['detailed_writes'] if w.write_necessity == WriteNecessity.ELIMINABLE]

        print(f"  Essential writes (cannot avoid): {len(essential_writes)}")
        print(f"  Eliminable writes (can remove):  {len(eliminable_writes)}")

        if eliminable_writes:
            eliminable_cells = sum(w.matrix_size[0] * w.matrix_size[1] for w in eliminable_writes)
            print(f"  Eliminable cells: {eliminable_cells:,} ({eliminable_cells/analysis_result['total_cells']*100:.1f}%)")

    def _classify_factor_type(self, factor_name: str) -> str:
        """Classify factor type from factor name"""
        if 'prior' in factor_name: return 'prior'
        elif 'odom' in factor_name: return 'odometry'
        elif 'meas' in factor_name or 'cam' in factor_name: return 'measurement'
        elif 'imu' in factor_name: return 'imu'
        elif 'dyn' in factor_name: return 'dynamics'
        elif 'cost' in factor_name: return 'cost'
        else: return 'generic'

    def _estimate_jacobian_size(self, connected_vars: List[str], nodes: Dict[str, List[str]]) -> Tuple[int, int]:
        """Estimate Jacobian matrix size"""
        total_var_dim = 0
        for var in connected_vars:
            for node_type, var_list in nodes.items():
                if var in var_list:
                    if node_type == 'poses': total_var_dim += 6
                    elif node_type == 'landmarks': total_var_dim += 3
                    else: total_var_dim += 3
                    break
        residual_dim = min(total_var_dim, 6)  # Most factors have residual <= 6
        return (residual_dim, total_var_dim)

    def _estimate_qr_size(self, var_type: str, num_factors: int) -> Tuple[int, int]:
        """Estimate QR decomposition matrix size"""
        var_dim = 6 if var_type == 'poses' else 3
        rows = num_factors * min(var_dim, 6)
        cols = var_dim
        return (rows, cols)


def main():
    """Main function for testing RRAM data analysis"""

    analyzer = RRAMDataAnalyzer()

    # Test applications
    test_applications = [
        ('robot', 'localization'),
        ('robot', 'control'),
        ('autovehicle', 'localization'),
    ]

    print("🔬 RRAM Data Write Analysis Tool")
    print("=" * 50)

    for app_name, algorithm in test_applications:
        try:
            analysis_result = analyzer.analyze_factor_graph_data_writes(app_name, algorithm)
            analyzer.print_detailed_analysis(analysis_result)

        except Exception as e:
            print(f"❌ Error analyzing {app_name} - {algorithm}: {e}")


if __name__ == "__main__":
    main()
