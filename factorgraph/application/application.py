import networkx as nx
import matplotlib.pyplot as plt
import numpy as np

# --- Reusable Plotting Function (Improved Version) ---
def plot_factor_graph(nodes_dict, factors_dict, title, filename, layout_type='trajectory'):
    """
    Generates and saves a clearer visualization of a factor graph.

    Args:
        nodes_dict (dict): Dictionary of nodes, e.g., {'poses': [...], 'landmarks': [...]}.
        factors_dict (dict): Dictionary mapping factor names to connected variables.
        title (str): The title for the plot.
        filename (str): Filename for saving the plot.
        layout_type (str): 'trajectory' for sequential layouts, 'spring' for others.
    """
    G = nx.Graph()
    pos = {}
    
    # --- 1. Add and Position Variable Nodes ---
    for node_type, nodes in nodes_dict.items():
        for i, node_name in enumerate(nodes):
            G.add_node(node_name, type=node_type)
            # Define positions for a clear layout
            if layout_type == 'trajectory':
                if node_type == 'poses' or node_type == 'states':
                    # Layout poses/states in a curve to avoid overlaps
                    angle = i * (np.pi / (len(nodes) / 2.0 + 3))
                    radius = 2.0 + i * 0.4
                    pos[node_name] = (radius * np.cos(angle), radius * np.sin(angle))
                elif node_type == 'landmarks':
                    angle = i * (2 * np.pi / len(nodes))
                    pos[node_name] = (8 * np.cos(angle), 8 * np.sin(angle))
                elif node_type == 'inputs':
                    state_pos = pos.get(f's{i}') or pos.get(f'e{i}')
                    if state_pos:
                        pos[node_name] = (state_pos[0], state_pos[1] - 2.5)
    
    # --- 2. Add Factor Nodes and Edges ---
    for factor_name, connected_vars in factors_dict.items():
        G.add_node(factor_name, type='factor')
        if layout_type == 'trajectory' and connected_vars:
            # Position factors at the centroid of connected variables
            avg_x = sum(pos[cn][0] for cn in connected_vars) / len(connected_vars)
            avg_y = sum(pos[cn][1] for cn in connected_vars) / len(connected_vars)
            pos[factor_name] = (avg_x, avg_y)
        for var_name in connected_vars:
            G.add_edge(factor_name, var_name)

    # Use spring layout if not a trajectory or for complex graphs
    if layout_type == 'spring' or not pos:
        # Increased k value to spread nodes apart
        pos = nx.spring_layout(G, seed=42, k=1.5, iterations=100)

    # --- 3. Style and Draw the Graph ---
    # Increased figure size for better clarity
    plt.figure(figsize=(22, 14))
    
    node_colors, node_sizes, labels = {}, {}, {}
    for node, data in G.nodes(data=True):
        labels[node] = node
        node_type = data.get('type', 'factor')
        node_category = 'variable'
        if node_type == 'poses':
            node_colors[node] = 'skyblue'
            node_sizes[node] = 800
        elif node_type == 'landmarks':
            node_colors[node] = 'lightgreen'
            node_sizes[node] = 700
        elif node_type in ['states', 'inputs']:
            node_colors[node] = 'lightgray'
            node_sizes[node] = 700
        else: # factor
            node_category = 'factor'
            node_colors[node] = 'salmon'
            node_sizes[node] = 180
            labels[node] = ''

    # Draw nodes and edges
    nx.draw_networkx_edges(G, pos, edge_color='gray', alpha=0.6)
    nx.draw_networkx_nodes(G, pos, nodelist=[n for n,d in G.nodes(data=True) if d.get('type') != 'factor'],
                           node_color=[node_colors[n] for n,d in G.nodes(data=True) if d.get('type') != 'factor'],
                           node_size=[node_sizes[n] for n,d in G.nodes(data=True) if d.get('type') != 'factor'])
    nx.draw_networkx_nodes(G, pos, nodelist=[n for n,d in G.nodes(data=True) if d.get('type') == 'factor'],
                           node_color='salmon', node_size=180, node_shape='s')
    nx.draw_networkx_labels(G, pos, labels, font_size=9)
    
    plt.title(title, fontsize=24, pad=20)
    
    # Create legend
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', label='Pose', markerfacecolor='skyblue', markersize=15),
        plt.Line2D([0], [0], marker='o', color='w', label='Landmark', markerfacecolor='lightgreen', markersize=15),
        plt.Line2D([0], [0], marker='o', color='w', label='State/Input', markerfacecolor='lightgray', markersize=15),
        plt.Line2D([0], [0], marker='s', color='w', label='Factor', markerfacecolor='salmon', markersize=10)
    ]
    plt.legend(handles=legend_elements, loc='upper right', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(filename)
    print(f"Graph with title '{title}' saved to {filename}")
    plt.close()


# --- Data Generation Functions (Identical to previous code) ---

# --- 1. Mobile Robot ---
def gen_robot_localization():
    n_poses, n_lms = 10, 4
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}
    for i in range(n_poses - 1): factors[f'f_odom{i}'] = [f'x{i}', f'x{i+1}']
    meas = [(0,0), (1,0), (2,1), (3,1), (4,2), (5,2), (6,3), (7,3), (8,3), (9,1)] # Loop closure
    for i, (p,l) in enumerate(meas): factors[f'f_meas{i}'] = [f'x{p}', f'l{l}']
    return nodes, factors, 'trajectory'

def gen_robot_planning():
    n_states = 8
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}
    for i in range(n_states - 1): factors[f'f_smooth{i}'] = [f's{i}', f's{i+1}']
    for i in range(n_states): factors[f'f_coll{i}'] = [f's{i}']
    return nodes, factors, 'trajectory'

def gen_robot_control():
    n_steps = 5
    nodes = {'states': [f'e{i}' for i in range(n_steps)], 'inputs': [f'u{i}' for i in range(n_steps-1)]}
    factors = {}
    for i in range(n_steps - 1):
        factors[f'f_dyn{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']
        factors[f'f_cost{i}'] = [f'e{i}', f'u{i}']
    factors[f'f_final_cost'] = [f'e{n_steps-1}']
    return nodes, factors, 'trajectory'

# --- 2. Manipulator ---
def gen_manipulator_localization():
    nodes = {'states': ['JointState']}
    factors = {'f_prior': ['JointState']}
    return nodes, factors, 'spring'

def gen_manipulator_planning():
    n_states = 10
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}
    for i in range(n_states - 1): factors[f'f_smooth{i}'] = [f's{i}', f's{i+1}']
    for i in range(n_states): factors[f'f_coll{i}'] = [f's{i}']
    return nodes, factors, 'trajectory'
    
def gen_manipulator_control():
    return gen_robot_control() # The structure is identical

# --- 3. Autonomous Vehicle ---
def gen_autovehicle_localization():
    n_poses, n_lms = 12, 5
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}
    for i in range(n_poses - 1): factors[f'f_odom{i}'] = [f'x{i}', f'x{i+1}']
    meas = [(0,0), (1,0), (2,1), (3,1), (4,2), (5,2), (6,3), (7,3), (8,4), (9,4), (11,1)] # Loop closure
    for i, (p,l) in enumerate(meas): factors[f'f_meas{i}'] = [f'x{p}', f'l{l}']
    return nodes, factors, 'trajectory'

def gen_autovehicle_planning():
    n_states = 8
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}
    for i in range(n_states - 1): factors[f'f_kinematics{i}'] = [f's{i}', f's{i+1}']
    for i in range(n_states): factors[f'f_coll{i}'] = [f's{i}']
    return nodes, factors, 'trajectory'

def gen_autovehicle_control():
    n_steps = 5
    nodes = {'states': [f'e{i}' for i in range(n_steps)], 'inputs': [f'u{i}' for i in range(n_steps-1)]}
    factors = {}
    for i in range(n_steps - 1):
        factors[f'f_dyn{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']
        factors[f'f_kin{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']
        factors[f'f_cost{i}'] = [f'e{i}', f'u{i}']
    factors[f'f_final_cost'] = [f'e{n_steps-1}']
    return nodes, factors, 'trajectory'

# --- 4. Quadrotor ---
def gen_quadrotor_localization(): # VIO
    n_poses, n_lms = 10, 5
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}
    for i in range(n_poses - 1): factors[f'f_imu{i}'] = [f'x{i}', f'x{i+1}'] # IMU factors
    meas = [(0,0), (1,0), (1,1), (2,1), (3,2), (4,2), (5,3), (6,3), (7,4), (8,4), (9,2)] # Loop closure
    for i, (p,l) in enumerate(meas): factors[f'f_cam{i}'] = [f'x{p}', f'l{l}'] # Camera factors
    # Use spring layout for better visualization of complex 3D structure
    return nodes, factors, 'spring'
    
def gen_quadrotor_planning():
    return gen_autovehicle_planning() # Structure is identical

def gen_quadrotor_control():
    return gen_autovehicle_control() # Structure is identical

# --- Main Execution Script ---
if __name__ == '__main__':
    all_graphs_to_generate = {
        "app1_robot_localization.png": ("Mobile Robot - Localization", gen_robot_localization),
        "app1_robot_planning.png": ("Mobile Robot - Planning", gen_robot_planning),
        "app1_robot_control.png": ("Mobile Robot - Control", gen_robot_control),
        "app2_manipulator_localization.png": ("Manipulator - Localization", gen_manipulator_localization),
        "app2_manipulator_planning.png": ("Manipulator - Planning", gen_manipulator_planning),
        "app2_manipulator_control.png": ("Manipulator - Control", gen_manipulator_control),
        "app3_autovehicle_localization.png": ("Autonomous Vehicle - Localization", gen_autovehicle_localization),
        "app3_autovehicle_planning.png": ("Autonomous Vehicle - Planning", gen_autovehicle_planning),
        "app3_autovehicle_control.png": ("Autonomous Vehicle - Control", gen_autovehicle_control),
        "app4_quadrotor_localization.png": ("Quadrotor - Localization (VIO)", gen_quadrotor_localization),
        "app4_quadrotor_planning.png": ("Quadrotor - Planning", gen_quadrotor_planning),
        "app4_quadrotor_control.png": ("Quadrotor - Control", gen_quadrotor_control),
    }

    for filename, (title, data_func) in all_graphs_to_generate.items():
        nodes, factors, layout = data_func()
        plot_factor_graph(nodes, factors, title, filename, layout_type=layout)

    print("\nGeneration of 12 factor graphs complete.")