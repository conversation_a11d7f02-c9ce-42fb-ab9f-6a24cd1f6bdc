#!/usr/bin/env python3
"""
RRAM Write Estimator for Factor Graph Applications

This tool estimates RRAM write operations when converting factor graph applications
directly from factorgraph/application to computational operations, without using
pre-existing slam_factorgraph configurations.

Key Analysis:
1. Factor computation operations (error and Jacobian calculation)
2. Variable elimination operations (QR decomposition)
3. Matrix write operations for each factor and elimination step
4. Total RRAM cell write count estimation

Author: Event-Driven-Simulator Team
"""

import sys
import os
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import json
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from factorgraph.application.application import *
    from mapping_scheduling.op_list import Operator
    print("Successfully imported factor graph applications.")
except ImportError as e:
    print(f"Error importing modules: {e}")
    raise

@dataclass
class FactorComputationCost:
    """Cost estimation for a single factor computation"""
    factor_name: str
    factor_type: str  # 'prior', 'odometry', 'measurement', 'dynamics', etc.
    connected_variables: List[str]
    error_computation_ops: int  # Number of matrix operations for error
    jacobian_computation_ops: int  # Number of matrix operations for Jacobians
    matrix_writes: int  # Number of RRAM matrix writes
    total_cells_written: int  # Total RRAM cells written

@dataclass
class VariableEliminationCost:
    """Cost estimation for variable elimination"""
    variable_name: str
    variable_dimension: int  # 2D pose=3, 3D pose=6, landmark=2/3
    connected_factors: List[str]
    qr_operations: int  # QR decomposition operations
    matrix_writes: int  # RRAM writes for QR matrices
    total_cells_written: int

@dataclass
class ApplicationRRAMCost:
    """Total RRAM cost for an application"""
    app_name: str
    algorithm: str
    total_variables: int
    total_factors: int
    factor_costs: List[FactorComputationCost]
    elimination_costs: List[VariableEliminationCost]
    total_write_operations: int
    total_cells_written: int
    execution_cycles: int

class FactorGraphRRAMEstimator:
    """
    Estimates RRAM write operations for factor graph applications
    """
    
    def __init__(self):
        # Standard dimensions for different variable types
        self.variable_dimensions = {
            'pose_2d': 3,      # x, y, theta
            'pose_3d': 6,      # x, y, z, roll, pitch, yaw
            'landmark_2d': 2,  # x, y
            'landmark_3d': 3,  # x, y, z
            'velocity': 3,     # vx, vy, vz
            'state': 6,        # position + velocity
            'input': 3,        # control inputs
        }
        
        # Matrix operation costs (estimated RRAM writes per operation)
        self.operation_costs = {
            'matrix_multiply': lambda m, n, k: m * n,  # Write result matrix
            'jacobian_compute': lambda vars, dim: vars * dim * dim,  # Write Jacobian blocks
            'qr_decomposition': lambda n: n * n * 2,  # Write Q and R matrices
            'error_compute': lambda dim: dim,  # Write error vector
        }
    
    def estimate_application_cost(self, app_name: str, algorithm: str, 
                                execution_cycles: int = 1) -> ApplicationRRAMCost:
        """
        Estimate RRAM write cost for a factor graph application
        
        Args:
            app_name: Application name ('robot', 'manipulator', 'autovehicle', 'quadrotor')
            algorithm: Algorithm type ('localization', 'planning', 'control')
            execution_cycles: Number of execution cycles to simulate
            
        Returns:
            ApplicationRRAMCost with detailed cost breakdown
        """
        print(f"\n🔍 Estimating RRAM costs for {app_name} - {algorithm}")
        
        # Get factor graph structure
        nodes, factors, layout = self._get_factor_graph(app_name, algorithm)
        
        # Analyze factor computation costs
        factor_costs = self._analyze_factor_costs(factors, nodes)
        
        # Analyze variable elimination costs
        elimination_costs = self._analyze_elimination_costs(nodes, factors)
        
        # Calculate totals
        total_factor_writes = sum(fc.total_cells_written for fc in factor_costs)
        total_elimination_writes = sum(ec.total_cells_written for ec in elimination_costs)
        total_writes = (total_factor_writes + total_elimination_writes) * execution_cycles
        
        total_operations = (len(factor_costs) + len(elimination_costs)) * execution_cycles
        
        return ApplicationRRAMCost(
            app_name=app_name,
            algorithm=algorithm,
            total_variables=sum(len(var_list) for var_list in nodes.values()),
            total_factors=len(factors),
            factor_costs=factor_costs,
            elimination_costs=elimination_costs,
            total_write_operations=total_operations,
            total_cells_written=total_writes,
            execution_cycles=execution_cycles
        )
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """Get factor graph structure from application generators"""
        
        generator_map = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator_func = generator_map.get((app_name, algorithm))
        if not generator_func:
            raise ValueError(f"Unknown application: {app_name} - {algorithm}")
        
        return generator_func()
    
    def _analyze_factor_costs(self, factors: Dict[str, List[str]], 
                            nodes: Dict[str, List[str]]) -> List[FactorComputationCost]:
        """Analyze RRAM write costs for factor computations"""
        
        factor_costs = []
        
        for factor_name, connected_vars in factors.items():
            # Determine factor type from name
            factor_type = self._classify_factor_type(factor_name)
            
            # Estimate variable dimensions
            var_dims = []
            for var in connected_vars:
                dim = self._estimate_variable_dimension(var, nodes)
                var_dims.append(dim)
            
            # Calculate computation costs
            error_ops, jacobian_ops, matrix_writes, total_cells = \
                self._calculate_factor_computation_cost(factor_type, var_dims)
            
            factor_cost = FactorComputationCost(
                factor_name=factor_name,
                factor_type=factor_type,
                connected_variables=connected_vars,
                error_computation_ops=error_ops,
                jacobian_computation_ops=jacobian_ops,
                matrix_writes=matrix_writes,
                total_cells_written=total_cells
            )
            
            factor_costs.append(factor_cost)
            
            print(f"  Factor {factor_name} ({factor_type}): {total_cells} RRAM cells")
        
        return factor_costs
    
    def _analyze_elimination_costs(self, nodes: Dict[str, List[str]], 
                                 factors: Dict[str, List[str]]) -> List[VariableEliminationCost]:
        """Analyze RRAM write costs for variable elimination"""
        
        elimination_costs = []
        
        # Process each variable for elimination
        all_variables = []
        for var_type, var_list in nodes.items():
            for var in var_list:
                all_variables.append((var, var_type))
        
        for var_name, var_type in all_variables:
            # Find factors connected to this variable
            connected_factors = [f for f, vars in factors.items() if var_name in vars]
            
            # Estimate variable dimension
            var_dim = self._get_variable_dimension_by_type(var_type)
            
            # Calculate elimination cost
            qr_ops, matrix_writes, total_cells = \
                self._calculate_elimination_cost(var_dim, len(connected_factors))
            
            elimination_cost = VariableEliminationCost(
                variable_name=var_name,
                variable_dimension=var_dim,
                connected_factors=connected_factors,
                qr_operations=qr_ops,
                matrix_writes=matrix_writes,
                total_cells_written=total_cells
            )
            
            elimination_costs.append(elimination_cost)
        
        return elimination_costs

    def _classify_factor_type(self, factor_name: str) -> str:
        """Classify factor type from factor name"""

        if 'prior' in factor_name:
            return 'prior'
        elif 'odom' in factor_name:
            return 'odometry'
        elif 'meas' in factor_name or 'cam' in factor_name:
            return 'measurement'
        elif 'imu' in factor_name:
            return 'imu'
        elif 'dyn' in factor_name:
            return 'dynamics'
        elif 'kin' in factor_name:
            return 'kinematics'
        elif 'cost' in factor_name:
            return 'cost'
        elif 'smooth' in factor_name:
            return 'smoothness'
        elif 'coll' in factor_name:
            return 'collision'
        elif 'start' in factor_name or 'goal' in factor_name:
            return 'boundary'
        else:
            return 'generic'

    def _estimate_variable_dimension(self, var_name: str, nodes: Dict[str, List[str]]) -> int:
        """Estimate variable dimension from variable name and node structure"""

        # Find which node type this variable belongs to
        for node_type, var_list in nodes.items():
            if var_name in var_list:
                return self._get_variable_dimension_by_type(node_type)

        # Default dimension if not found
        return 3

    def _get_variable_dimension_by_type(self, var_type: str) -> int:
        """Get variable dimension by type"""

        if var_type == 'poses':
            return 6  # 3D pose: x, y, z, roll, pitch, yaw
        elif var_type == 'landmarks':
            return 3  # 3D landmark: x, y, z
        elif var_type == 'states':
            return 6  # State vector (position + velocity or similar)
        elif var_type == 'inputs':
            return 3  # Control inputs
        else:
            return 3  # Default

    def _calculate_factor_computation_cost(self, factor_type: str,
                                         var_dims: List[int]) -> Tuple[int, int, int, int]:
        """Calculate computation cost for a factor"""

        # Base costs for different factor types
        factor_costs = {
            'prior': (1, 1),           # (error_ops, jacobian_ops)
            'odometry': (2, 2),        # Between-factor
            'measurement': (2, 2),     # Observation factor
            'imu': (3, 3),            # IMU factor (more complex)
            'dynamics': (4, 4),        # Dynamics constraint
            'kinematics': (3, 3),      # Kinematic constraint
            'cost': (1, 1),           # Cost factor
            'smoothness': (2, 2),      # Smoothness constraint
            'collision': (1, 1),       # Collision constraint
            'boundary': (1, 1),        # Boundary condition
            'generic': (2, 2),         # Default
        }

        base_error_ops, base_jacobian_ops = factor_costs.get(factor_type, (2, 2))

        # Scale by variable dimensions
        total_var_dim = sum(var_dims)

        # Error computation: typically results in a vector of size total_var_dim
        error_ops = base_error_ops
        error_cells = total_var_dim

        # Jacobian computation: matrix of size (residual_dim x total_var_dim)
        residual_dim = min(total_var_dim, 6)  # Most factors have residual <= 6
        jacobian_ops = base_jacobian_ops * len(var_dims)
        jacobian_cells = residual_dim * total_var_dim

        # Matrix writes: error vector + Jacobian matrix
        matrix_writes = 2  # Error vector write + Jacobian matrix write
        total_cells = error_cells + jacobian_cells

        return error_ops, jacobian_ops, matrix_writes, total_cells

    def _calculate_elimination_cost(self, var_dim: int, num_factors: int) -> Tuple[int, int, int]:
        """Calculate cost for eliminating a variable"""

        # QR decomposition cost depends on the size of the local system
        # Each factor contributes a block to the Jacobian
        local_jacobian_rows = num_factors * min(var_dim, 6)  # Each factor contributes up to 6 rows
        local_jacobian_cols = var_dim

        # QR decomposition operations
        qr_ops = 1  # One QR decomposition per variable

        # Matrix writes for QR decomposition
        # Write Q matrix: local_jacobian_rows x local_jacobian_rows
        # Write R matrix: local_jacobian_rows x local_jacobian_cols
        q_matrix_cells = local_jacobian_rows * local_jacobian_rows
        r_matrix_cells = local_jacobian_rows * local_jacobian_cols

        matrix_writes = 2  # Q matrix + R matrix
        total_cells = q_matrix_cells + r_matrix_cells

        return qr_ops, matrix_writes, total_cells

    def print_detailed_cost_analysis(self, cost: ApplicationRRAMCost):
        """Print detailed cost analysis"""

        print(f"\n{'='*80}")
        print(f"RRAM WRITE COST ANALYSIS: {cost.app_name.upper()} - {cost.algorithm.upper()}")
        print(f"{'='*80}")

        print(f"\n📊 FACTOR GRAPH STRUCTURE:")
        print(f"  Total Variables:        {cost.total_variables}")
        print(f"  Total Factors:          {cost.total_factors}")
        print(f"  Execution Cycles:       {cost.execution_cycles}")

        print(f"\n🔧 FACTOR COMPUTATION COSTS:")
        factor_total_cells = sum(fc.total_cells_written for fc in cost.factor_costs)
        print(f"  Total Factor Operations: {len(cost.factor_costs)}")
        print(f"  Total Cells (Factors):   {factor_total_cells:,}")

        # Group factors by type
        factor_by_type = defaultdict(list)
        for fc in cost.factor_costs:
            factor_by_type[fc.factor_type].append(fc)

        for factor_type, factors in factor_by_type.items():
            count = len(factors)
            total_cells = sum(f.total_cells_written for f in factors)
            avg_cells = total_cells / count if count > 0 else 0
            print(f"    {factor_type.capitalize():<15}: {count:3d} factors, "
                  f"{total_cells:6,} cells ({avg_cells:.0f} avg)")

        print(f"\n🔄 VARIABLE ELIMINATION COSTS:")
        elim_total_cells = sum(ec.total_cells_written for ec in cost.elimination_costs)
        print(f"  Total Elimination Ops:   {len(cost.elimination_costs)}")
        print(f"  Total Cells (Elimination): {elim_total_cells:,}")

        # Group eliminations by variable dimension
        elim_by_dim = defaultdict(list)
        for ec in cost.elimination_costs:
            elim_by_dim[ec.variable_dimension].append(ec)

        for dim, eliminations in elim_by_dim.items():
            count = len(eliminations)
            total_cells = sum(e.total_cells_written for e in eliminations)
            avg_cells = total_cells / count if count > 0 else 0
            print(f"    {dim}D Variables<15>: {count:3d} variables, "
                  f"{total_cells:6,} cells ({avg_cells:.0f} avg)")

        print(f"\n⚡ TOTAL RRAM IMPACT:")
        print(f"  Total Write Operations:  {cost.total_write_operations:,}")
        print(f"  Total RRAM Cells Written: {cost.total_cells_written:,}")
        print(f"  Cells per Execution:     {cost.total_cells_written // cost.execution_cycles:,}")
        print(f"  Factor vs Elimination:   {factor_total_cells:,} vs {elim_total_cells:,}")

        # Calculate percentages
        factor_pct = (factor_total_cells / (factor_total_cells + elim_total_cells)) * 100
        elim_pct = 100 - factor_pct
        print(f"  Cost Distribution:       {factor_pct:.1f}% factors, {elim_pct:.1f}% elimination")


def main():
    """Main function for testing RRAM write estimation"""

    estimator = FactorGraphRRAMEstimator()

    # Test applications
    test_applications = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]

    execution_cycles = 100

    print("🚀 Factor Graph RRAM Write Estimation")
    print(f"Analyzing {len(test_applications)} applications with {execution_cycles} execution cycles")

    all_costs = []

    for app_name, algorithm in test_applications:
        try:
            cost = estimator.estimate_application_cost(app_name, algorithm, execution_cycles)
            estimator.print_detailed_cost_analysis(cost)
            all_costs.append(cost)

        except Exception as e:
            print(f"❌ Error analyzing {app_name} - {algorithm}: {e}")

    # Summary comparison
    if len(all_costs) > 1:
        print(f"\n{'='*80}")
        print(f"COMPARISON ACROSS APPLICATIONS")
        print(f"{'='*80}")

        print(f"{'Application':<25} {'Variables':<10} {'Factors':<8} {'Total Cells':<12} {'Cells/Cycle':<12}")
        print(f"{'-'*80}")

        for cost in all_costs:
            cells_per_cycle = cost.total_cells_written // cost.execution_cycles
            print(f"{cost.app_name}_{cost.algorithm:<25} {cost.total_variables:<10} "
                  f"{cost.total_factors:<8} {cost.total_cells_written:<12,} {cells_per_cycle:<12,}")


if __name__ == "__main__":
    main()
