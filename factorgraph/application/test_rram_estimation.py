#!/usr/bin/env python3
"""
Test script for Factor Graph RRAM Write Estimation

This script demonstrates how to estimate RRAM write operations when converting
factor graph applications directly to computational operations.

Usage:
    python test_rram_estimation.py
    python test_rram_estimation.py --app robot --algorithm localization --cycles 1000
    python test_rram_estimation.py --all
"""

import argparse
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rram_write_estimator import FactorGraphRRAMEstimator

def test_single_application(app_name: str, algorithm: str, cycles: int = 100):
    """Test RRAM estimation for a single application"""
    
    print(f"🧪 Testing RRAM estimation: {app_name} - {algorithm}")
    
    estimator = FactorGraphRRAMEstimator()
    
    try:
        cost = estimator.estimate_application_cost(app_name, algorithm, cycles)
        estimator.print_detailed_cost_analysis(cost)
        
        # Key insights
        print(f"\n🔍 KEY INSIGHTS:")
        cells_per_cycle = cost.total_cells_written // cycles
        print(f"  • RRAM cells written per execution: {cells_per_cycle:,}")
        print(f"  • Factor computation dominance: {(sum(fc.total_cells_written for fc in cost.factor_costs) / cost.total_cells_written * 100):.1f}%")
        print(f"  • Average cells per factor: {sum(fc.total_cells_written for fc in cost.factor_costs) // len(cost.factor_costs):,}")
        print(f"  • Average cells per variable elimination: {sum(ec.total_cells_written for ec in cost.elimination_costs) // len(cost.elimination_costs):,}")
        
        return cost
        
    except Exception as e:
        print(f"❌ Error testing {app_name} - {algorithm}: {e}")
        return None

def compare_applications(cycles: int = 100):
    """Compare RRAM costs across different applications"""
    
    print(f"⚖️  Comparing RRAM costs across applications")
    
    applications = [
        ('robot', 'localization'),
        ('robot', 'planning'),
        ('robot', 'control'),
        ('autovehicle', 'localization'),
        ('quadrotor', 'localization'),
    ]
    
    estimator = FactorGraphRRAMEstimator()
    results = []
    
    for app_name, algorithm in applications:
        try:
            cost = estimator.estimate_application_cost(app_name, algorithm, cycles)
            results.append(cost)
            print(f"✅ {app_name} - {algorithm}: {cost.total_cells_written:,} cells")
        except Exception as e:
            print(f"❌ {app_name} - {algorithm}: {e}")
    
    if results:
        print_comparison_table(results)
    
    return results

def print_comparison_table(costs):
    """Print comparison table of RRAM costs"""
    
    print(f"\n{'='*100}")
    print(f"RRAM WRITE COST COMPARISON")
    print(f"{'='*100}")
    
    print(f"{'Application':<25} {'Vars':<6} {'Factors':<8} {'Factor Cells':<12} "
          f"{'Elim Cells':<12} {'Total Cells':<12} {'Cells/Cycle':<12}")
    print(f"{'-'*100}")
    
    for cost in costs:
        factor_cells = sum(fc.total_cells_written for fc in cost.factor_costs)
        elim_cells = sum(ec.total_cells_written for ec in cost.elimination_costs)
        cells_per_cycle = cost.total_cells_written // cost.execution_cycles
        
        print(f"{cost.app_name}_{cost.algorithm:<25} {cost.total_variables:<6} "
              f"{cost.total_factors:<8} {factor_cells:<12,} {elim_cells:<12,} "
              f"{cost.total_cells_written:<12,} {cells_per_cycle:<12,}")
    
    # Find most/least expensive
    if len(costs) > 1:
        max_cost = max(costs, key=lambda c: c.total_cells_written)
        min_cost = min(costs, key=lambda c: c.total_cells_written)
        
        print(f"\n📈 ANALYSIS:")
        print(f"  Most expensive:  {max_cost.app_name}_{max_cost.algorithm} "
              f"({max_cost.total_cells_written:,} cells)")
        print(f"  Least expensive: {min_cost.app_name}_{min_cost.algorithm} "
              f"({min_cost.total_cells_written:,} cells)")
        print(f"  Cost ratio:      {max_cost.total_cells_written / min_cost.total_cells_written:.1f}x")

def analyze_scaling_behavior():
    """Analyze how RRAM costs scale with execution cycles"""
    
    print(f"📊 Analyzing scaling behavior with execution cycles")
    
    estimator = FactorGraphRRAMEstimator()
    app_name, algorithm = 'robot', 'localization'
    
    cycle_counts = [1, 10, 100, 1000]
    results = []
    
    for cycles in cycle_counts:
        try:
            cost = estimator.estimate_application_cost(app_name, algorithm, cycles)
            results.append((cycles, cost.total_cells_written))
            print(f"  {cycles:4d} cycles: {cost.total_cells_written:8,} total cells")
        except Exception as e:
            print(f"  {cycles:4d} cycles: Error - {e}")
    
    if len(results) > 1:
        print(f"\n📈 SCALING ANALYSIS:")
        base_cycles, base_cells = results[0]
        for cycles, total_cells in results[1:]:
            expected_cells = base_cells * (cycles / base_cycles)
            actual_ratio = total_cells / base_cells
            expected_ratio = cycles / base_cycles
            print(f"  {cycles:4d} cycles: {actual_ratio:.1f}x scaling "
                  f"(expected {expected_ratio:.1f}x) - {'✅' if abs(actual_ratio - expected_ratio) < 0.1 else '⚠️'}")

def estimate_hardware_requirements(cost, target_frequency_hz: float = 1000.0):
    """Estimate hardware requirements for real-time execution"""
    
    print(f"\n🔧 HARDWARE REQUIREMENTS ESTIMATION")
    print(f"Target execution frequency: {target_frequency_hz} Hz")
    
    cells_per_execution = cost.total_cells_written // cost.execution_cycles
    cells_per_second = cells_per_execution * target_frequency_hz
    
    # Assume typical RRAM write time and energy
    write_time_ns = 100  # 100ns per write operation
    write_energy_pj = 10  # 10pJ per write operation
    
    total_write_time_us = (cells_per_second * write_time_ns) / 1000
    total_write_energy_mw = (cells_per_second * write_energy_pj) / 1000
    
    print(f"  RRAM cells written per second: {cells_per_second:,}")
    print(f"  Total write time per second:   {total_write_time_us:.1f} μs ({total_write_time_us/10000:.1f}% duty cycle)")
    print(f"  Total write energy per second: {total_write_energy_mw:.1f} mW")
    
    # Memory requirements
    max_matrix_size = max((fc.total_cells_written for fc in cost.factor_costs), default=0)
    print(f"  Largest matrix size:           {max_matrix_size:,} cells")
    print(f"  Estimated RRAM array size:     {cells_per_execution * 2:,} cells (2x for double buffering)")

def main():
    """Main function with command-line interface"""
    
    parser = argparse.ArgumentParser(
        description="Estimate RRAM write operations for factor graph applications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_rram_estimation.py
  python test_rram_estimation.py --app robot --algorithm localization
  python test_rram_estimation.py --app autovehicle --algorithm localization --cycles 1000
  python test_rram_estimation.py --compare
  python test_rram_estimation.py --scaling
        """
    )
    
    parser.add_argument('--app', type=str,
                       choices=['robot', 'manipulator', 'autovehicle', 'quadrotor'],
                       help='Application to test')
    parser.add_argument('--algorithm', type=str,
                       choices=['localization', 'planning', 'control'],
                       help='Algorithm to test')
    parser.add_argument('--cycles', type=int, default=100,
                       help='Number of execution cycles (default: 100)')
    parser.add_argument('--compare', action='store_true',
                       help='Compare costs across applications')
    parser.add_argument('--scaling', action='store_true',
                       help='Analyze scaling behavior')
    parser.add_argument('--hardware', action='store_true',
                       help='Estimate hardware requirements')
    
    args = parser.parse_args()
    
    print("🔬 Factor Graph RRAM Write Estimation Tool")
    print("=" * 50)
    
    if args.scaling:
        analyze_scaling_behavior()
        
    elif args.compare:
        compare_applications(args.cycles)
        
    elif args.app and args.algorithm:
        cost = test_single_application(args.app, args.algorithm, args.cycles)
        if cost and args.hardware:
            estimate_hardware_requirements(cost)
            
    else:
        # Default: test robot localization
        print("No specific test specified. Running default: robot localization")
        cost = test_single_application('robot', 'localization', args.cycles)
        if cost:
            estimate_hardware_requirements(cost)
        
        print(f"\n💡 TIP: Use --help to see all available options")
        print(f"💡 TIP: Try --compare to see costs across applications")
        print(f"💡 TIP: Try --scaling to analyze scaling behavior")

if __name__ == "__main__":
    main()
