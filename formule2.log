🚀 基于论文Table 2的正确因子特定分析
================================================================================
重要修正:
1. 不是所有因子都用SLAM旋转+平移公式
2. 每种因子类型有不同的误差公式
3. 基于具体公式建立operation过程
4. 统计所有被遗漏的写入操作


🔍 基于因子类型分析: robot - localization
================================================================================
📊 算法信息:
   变量维度: 3
   因子类型: ['LiDAR', 'GPS']
   因子总数: 212

📋 LiDAR 因子分析:
   数学公式: f(xi, xj) = (xi ⊖ xj) ⊖ zij
   误差公式: ξ1 ⊕ ξ2 = -Log(R1 R2), t1 + R1 t2
   具体操作过程:
      Store: xi - 6 次 (RRAM写入)
      Store: xj - 6 次 (RRAM写入)
      Store: zij - 6 次 (RRAM写入)
      Exp: R1 = Exp(φ1) - 9 次 (SIMD计算)
      Exp: R2 = Exp(φ2) - 9 次 (SIMD计算)
      DynamicVMM: R1 * R2 - 9 次 (RRAM写入)
      LOG: -Log(R1 R2) - 3 次 (SIMD计算)
      VP: t1 + R1*t2 - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 36 次 (RRAM写入)
   每因子RRAM写入: 63
   因子数量: 212
   该类型总写入: 13,356
   遗漏的SIMD操作: 5,088

📋 GPS 因子分析:
   数学公式: f(x) = h(x) - z
   误差公式: e = position(x) - gps_measurement
   具体操作过程:
      Store: x - 3 次 (RRAM写入)
      Store: z - 3 次 (RRAM写入)
      VP: position(x) - 3 次 (SIMD计算)
      VP: e = pos - z - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 9 次 (RRAM写入)
   每因子RRAM写入: 15
   因子数量: 212
   该类型总写入: 3,180
   遗漏的SIMD操作: 1,272

🎯 robot - localization 总结:
   总RRAM写入: 16,536
   之前遗漏的写入: 6,360
   修正比例: 38.5%


🔍 基于因子类型分析: robot - planning
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['Smooth', 'Collision-free']
   因子总数: 52

📋 Smooth 因子分析:
   数学公式: f(xi, xi+1) = ||xi+1 - xi||²
   误差公式: e = xi+1 - xi
   具体操作过程:
      Store: xi - 6 次 (RRAM写入)
      Store: xi+1 - 6 次 (RRAM写入)
      VP: e = xi+1 - xi - 6 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 10 次 (RRAM写入)
   每因子RRAM写入: 22
   因子数量: 52
   该类型总写入: 1,144
   遗漏的SIMD操作: 312

📋 Collision-free 因子分析:
   数学公式: f(x) = distance(x, obstacles) - threshold
   误差公式: e = d_min - d(x, obs)
   具体操作过程:
      Store: x - 6 次 (RRAM写入)
      Store: obstacles - 10 次 (RRAM写入)
      DynamicVMM: distance计算 - 10 次 (RRAM写入)
      VP: min distance - 1 次 (SIMD计算)
      VP: e = d_min - threshold - 1 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 6 次 (RRAM写入)
   每因子RRAM写入: 32
   因子数量: 52
   该类型总写入: 1,664
   遗漏的SIMD操作: 104

🎯 robot - planning 总结:
   总RRAM写入: 2,808
   之前遗漏的写入: 416
   修正比例: 14.8%


🔍 基于因子类型分析: robot - control
================================================================================
📊 算法信息:
   变量维度: (3, 2)
   因子类型: ['Dynamics']
   因子总数: 61

📋 Dynamics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - dynamic_model(xi, ui)
   误差公式: e = xi+1 - f_dyn(xi, ui)
   具体操作过程:
      Store: xi - 3 次 (RRAM写入)
      Store: ui - 2 次 (RRAM写入)
      Store: xi+1 - 3 次 (RRAM写入)
      DynamicVMM: dynamic_model - 6 次 (RRAM写入)
      VP: e = xi+1 - f_dyn - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 24 次 (RRAM写入)
   每因子RRAM写入: 38
   因子数量: 61
   该类型总写入: 2,318
   遗漏的SIMD操作: 183

🎯 robot - control 总结:
   总RRAM写入: 2,318
   之前遗漏的写入: 183
   修正比例: 7.9%


🔍 基于因子类型分析: manipulator - localization
================================================================================
📊 算法信息:
   变量维度: 2
   因子类型: ['Prior']
   因子总数: 1

📋 Prior 因子分析:
   数学公式: f(x) = x - x0
   误差公式: e = x - prior_value
   具体操作过程:
      Store: x - 2 次 (RRAM写入)
      Store: x0 - 2 次 (RRAM写入)
      VP: e = x - x0 - 2 次 (SIMD计算)
      StaticVMM: Jacobian = I - 4 次 (RRAM写入)
   每因子RRAM写入: 8
   因子数量: 1
   该类型总写入: 8
   遗漏的SIMD操作: 2

🎯 manipulator - localization 总结:
   总RRAM写入: 8
   之前遗漏的写入: 2
   修正比例: 25.0%


🔍 基于因子类型分析: manipulator - planning
================================================================================
📊 算法信息:
   变量维度: 4
   因子类型: ['Smooth', 'Collision-free']
   因子总数: 59

📋 Smooth 因子分析:
   数学公式: f(xi, xi+1) = ||xi+1 - xi||²
   误差公式: e = xi+1 - xi
   具体操作过程:
      Store: xi - 4 次 (RRAM写入)
      Store: xi+1 - 4 次 (RRAM写入)
      VP: e = xi+1 - xi - 4 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 10 次 (RRAM写入)
   每因子RRAM写入: 18
   因子数量: 59
   该类型总写入: 1,062
   遗漏的SIMD操作: 236

📋 Collision-free 因子分析:
   数学公式: f(x) = distance(x, obstacles) - threshold
   误差公式: e = d_min - d(x, obs)
   具体操作过程:
      Store: x - 4 次 (RRAM写入)
      Store: obstacles - 10 次 (RRAM写入)
      DynamicVMM: distance计算 - 10 次 (RRAM写入)
      VP: min distance - 1 次 (SIMD计算)
      VP: e = d_min - threshold - 1 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 4 次 (RRAM写入)
   每因子RRAM写入: 28
   因子数量: 59
   该类型总写入: 1,652
   遗漏的SIMD操作: 118

🎯 manipulator - planning 总结:
   总RRAM写入: 2,714
   之前遗漏的写入: 354
   修正比例: 13.0%


🔍 基于因子类型分析: manipulator - control
================================================================================
📊 算法信息:
   变量维度: (2, 2)
   因子类型: ['Dynamics']
   因子总数: 61

📋 Dynamics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - dynamic_model(xi, ui)
   误差公式: e = xi+1 - f_dyn(xi, ui)
   具体操作过程:
      Store: xi - 2 次 (RRAM写入)
      Store: ui - 2 次 (RRAM写入)
      Store: xi+1 - 2 次 (RRAM写入)
      DynamicVMM: dynamic_model - 4 次 (RRAM写入)
      VP: e = xi+1 - f_dyn - 2 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 12 次 (RRAM写入)
   每因子RRAM写入: 22
   因子数量: 61
   该类型总写入: 1,342
   遗漏的SIMD操作: 122

🎯 manipulator - control 总结:
   总RRAM写入: 1,342
   之前遗漏的写入: 122
   修正比例: 9.1%


🔍 基于因子类型分析: autovehicle - localization
================================================================================
📊 算法信息:
   变量维度: 3
   因子类型: ['LiDAR', 'GPS']
   因子总数: 251

📋 LiDAR 因子分析:
   数学公式: f(xi, xj) = (xi ⊖ xj) ⊖ zij
   误差公式: ξ1 ⊕ ξ2 = -Log(R1 R2), t1 + R1 t2
   具体操作过程:
      Store: xi - 6 次 (RRAM写入)
      Store: xj - 6 次 (RRAM写入)
      Store: zij - 6 次 (RRAM写入)
      Exp: R1 = Exp(φ1) - 9 次 (SIMD计算)
      Exp: R2 = Exp(φ2) - 9 次 (SIMD计算)
      DynamicVMM: R1 * R2 - 9 次 (RRAM写入)
      LOG: -Log(R1 R2) - 3 次 (SIMD计算)
      VP: t1 + R1*t2 - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 36 次 (RRAM写入)
   每因子RRAM写入: 63
   因子数量: 251
   该类型总写入: 15,813
   遗漏的SIMD操作: 6,024

📋 GPS 因子分析:
   数学公式: f(x) = h(x) - z
   误差公式: e = position(x) - gps_measurement
   具体操作过程:
      Store: x - 3 次 (RRAM写入)
      Store: z - 3 次 (RRAM写入)
      VP: position(x) - 3 次 (SIMD计算)
      VP: e = pos - z - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 9 次 (RRAM写入)
   每因子RRAM写入: 15
   因子数量: 251
   该类型总写入: 3,765
   遗漏的SIMD操作: 1,506

🎯 autovehicle - localization 总结:
   总RRAM写入: 19,578
   之前遗漏的写入: 7,530
   修正比例: 38.5%


🔍 基于因子类型分析: autovehicle - planning
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['Smooth', 'Collision-free', 'Kinematics']
   因子总数: 17

📋 Smooth 因子分析:
   数学公式: f(xi, xi+1) = ||xi+1 - xi||²
   误差公式: e = xi+1 - xi
   具体操作过程:
      Store: xi - 6 次 (RRAM写入)
      Store: xi+1 - 6 次 (RRAM写入)
      VP: e = xi+1 - xi - 6 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 10 次 (RRAM写入)
   每因子RRAM写入: 22
   因子数量: 17
   该类型总写入: 374
   遗漏的SIMD操作: 102

📋 Collision-free 因子分析:
   数学公式: f(x) = distance(x, obstacles) - threshold
   误差公式: e = d_min - d(x, obs)
   具体操作过程:
      Store: x - 6 次 (RRAM写入)
      Store: obstacles - 10 次 (RRAM写入)
      DynamicVMM: distance计算 - 10 次 (RRAM写入)
      VP: min distance - 1 次 (SIMD计算)
      VP: e = d_min - threshold - 1 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 6 次 (RRAM写入)
   每因子RRAM写入: 32
   因子数量: 17
   该类型总写入: 544
   遗漏的SIMD操作: 34

📋 Kinematics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - kinematic_model(xi, ui)
   误差公式: e = xi+1 - f_kin(xi, ui)
   具体操作过程:
      Store: xi - 12 次 (RRAM写入)
      Store: ui - 12 次 (RRAM写入)
      Store: xi+1 - 12 次 (RRAM写入)
      DynamicVMM: kinematic_model - 12 次 (RRAM写入)
      VP: e = xi+1 - f_kin - 12 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 12 次 (RRAM写入)
   每因子RRAM写入: 60
   因子数量: 17
   该类型总写入: 1,020
   遗漏的SIMD操作: 204

🎯 autovehicle - planning 总结:
   总RRAM写入: 1,938
   之前遗漏的写入: 340
   修正比例: 17.5%


🔍 基于因子类型分析: autovehicle - control
================================================================================
📊 算法信息:
   变量维度: (5, 2)
   因子类型: ['Kinematics', 'Dynamics']
   因子总数: 13

📋 Kinematics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - kinematic_model(xi, ui)
   误差公式: e = xi+1 - f_kin(xi, ui)
   具体操作过程:
      Store: xi - 5 次 (RRAM写入)
      Store: ui - 2 次 (RRAM写入)
      Store: xi+1 - 5 次 (RRAM写入)
      DynamicVMM: kinematic_model - 5 次 (RRAM写入)
      VP: e = xi+1 - f_kin - 5 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 60 次 (RRAM写入)
   每因子RRAM写入: 77
   因子数量: 13
   该类型总写入: 1,001
   遗漏的SIMD操作: 65

📋 Dynamics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - dynamic_model(xi, ui)
   误差公式: e = xi+1 - f_dyn(xi, ui)
   具体操作过程:
      Store: xi - 5 次 (RRAM写入)
      Store: ui - 2 次 (RRAM写入)
      Store: xi+1 - 5 次 (RRAM写入)
      DynamicVMM: dynamic_model - 10 次 (RRAM写入)
      VP: e = xi+1 - f_dyn - 5 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 60 次 (RRAM写入)
   每因子RRAM写入: 82
   因子数量: 13
   该类型总写入: 1,066
   遗漏的SIMD操作: 65

🎯 autovehicle - control 总结:
   总RRAM写入: 2,067
   之前遗漏的写入: 130
   修正比例: 6.3%


🔍 基于因子类型分析: quadrotor - localization
================================================================================
📊 算法信息:
   变量维度: 6
   因子类型: ['IMU', 'Prior']
   因子总数: 21

📋 IMU 因子分析:
   数学公式: f(xi, xi+1, vi, bi) = IMU_model
   误差公式: e = [eω, ea] = [ω - (ωm - bω), a - (am - ba)]
   具体操作过程:
      Store: xi - 6 次 (RRAM写入)
      Store: xi+1 - 6 次 (RRAM写入)
      Store: vi - 3 次 (RRAM写入)
      Store: bi - 6 次 (RRAM写入)
      Store: ωm - 3 次 (RRAM写入)
      Store: am - 3 次 (RRAM写入)
      VP: ω - (ωm - bω) - 3 次 (SIMD计算)
      VP: a - (am - ba) - 3 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 108 次 (RRAM写入)
   每因子RRAM写入: 135
   因子数量: 21
   该类型总写入: 2,835
   遗漏的SIMD操作: 126

📋 Prior 因子分析:
   数学公式: f(x) = x - x0
   误差公式: e = x - prior_value
   具体操作过程:
      Store: x - 6 次 (RRAM写入)
      Store: x0 - 6 次 (RRAM写入)
      VP: e = x - x0 - 6 次 (SIMD计算)
      StaticVMM: Jacobian = I - 36 次 (RRAM写入)
   每因子RRAM写入: 48
   因子数量: 21
   该类型总写入: 1,008
   遗漏的SIMD操作: 126

🎯 quadrotor - localization 总结:
   总RRAM写入: 3,843
   之前遗漏的写入: 252
   修正比例: 6.6%


🔍 基于因子类型分析: quadrotor - planning
================================================================================
📊 算法信息:
   变量维度: 12
   因子类型: ['Smooth', 'Collision-free', 'Kinematics']
   因子总数: 17

📋 Smooth 因子分析:
   数学公式: f(xi, xi+1) = ||xi+1 - xi||²
   误差公式: e = xi+1 - xi
   具体操作过程:
      Store: xi - 12 次 (RRAM写入)
      Store: xi+1 - 12 次 (RRAM写入)
      VP: e = xi+1 - xi - 12 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 10 次 (RRAM写入)
   每因子RRAM写入: 34
   因子数量: 17
   该类型总写入: 578
   遗漏的SIMD操作: 204

📋 Collision-free 因子分析:
   数学公式: f(x) = distance(x, obstacles) - threshold
   误差公式: e = d_min - d(x, obs)
   具体操作过程:
      Store: x - 12 次 (RRAM写入)
      Store: obstacles - 10 次 (RRAM写入)
      DynamicVMM: distance计算 - 10 次 (RRAM写入)
      VP: min distance - 1 次 (SIMD计算)
      VP: e = d_min - threshold - 1 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 12 次 (RRAM写入)
   每因子RRAM写入: 44
   因子数量: 17
   该类型总写入: 748
   遗漏的SIMD操作: 34

📋 Kinematics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - kinematic_model(xi, ui)
   误差公式: e = xi+1 - f_kin(xi, ui)
   具体操作过程:
      Store: xi - 24 次 (RRAM写入)
      Store: ui - 24 次 (RRAM写入)
      Store: xi+1 - 24 次 (RRAM写入)
      DynamicVMM: kinematic_model - 24 次 (RRAM写入)
      VP: e = xi+1 - f_kin - 24 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 24 次 (RRAM写入)
   每因子RRAM写入: 120
   因子数量: 17
   该类型总写入: 2,040
   遗漏的SIMD操作: 408

🎯 quadrotor - planning 总结:
   总RRAM写入: 3,366
   之前遗漏的写入: 646
   修正比例: 19.2%


🔍 基于因子类型分析: quadrotor - control
================================================================================
📊 算法信息:
   变量维度: (12, 5)
   因子类型: ['Kinematics', 'Dynamics']
   因子总数: 13

📋 Kinematics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - kinematic_model(xi, ui)
   误差公式: e = xi+1 - f_kin(xi, ui)
   具体操作过程:
      Store: xi - 12 次 (RRAM写入)
      Store: ui - 5 次 (RRAM写入)
      Store: xi+1 - 12 次 (RRAM写入)
      DynamicVMM: kinematic_model - 12 次 (RRAM写入)
      VP: e = xi+1 - f_kin - 12 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 348 次 (RRAM写入)
   每因子RRAM写入: 389
   因子数量: 13
   该类型总写入: 5,057
   遗漏的SIMD操作: 156

📋 Dynamics 因子分析:
   数学公式: f(xi, ui, xi+1) = xi+1 - dynamic_model(xi, ui)
   误差公式: e = xi+1 - f_dyn(xi, ui)
   具体操作过程:
      Store: xi - 12 次 (RRAM写入)
      Store: ui - 5 次 (RRAM写入)
      Store: xi+1 - 12 次 (RRAM写入)
      DynamicVMM: dynamic_model - 24 次 (RRAM写入)
      VP: e = xi+1 - f_dyn - 12 次 (SIMD计算)
      StaticVMM: Jacobian计算 - 348 次 (RRAM写入)
   每因子RRAM写入: 401
   因子数量: 13
   该类型总写入: 5,213
   遗漏的SIMD操作: 156

🎯 quadrotor - control 总结:
   总RRAM写入: 10,270
   之前遗漏的写入: 312
   修正比例: 3.0%

🎯 总结
================================================================================
12个算法总RRAM写入: 66,788
平均每算法: 5,565

关键修正:
1. 基于论文Table 2的真实因子类型
2. 每种因子有不同的数学公式和操作过程
3. 统计了之前遗漏的SIMD操作
4. 提供了准确的硬件设计数据
