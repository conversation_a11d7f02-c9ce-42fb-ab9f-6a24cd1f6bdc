#!/usr/bin/env python3
"""
基于slam_factorgraph.py复杂操作流程的12个算法RRAM写入分析

准确分析12个不同算法的RRAM写入次数，考虑：
1. 实际的操作类型分类 (StaticVMM vs DynamicVMM vs SIMD)
2. 基于slam_factorgraph.py的复杂多步骤流程
3. 每个算法的实际因子图结构和规模
4. Orianna架构的硬件特性
"""

import sys
import numpy as np
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import json

# Add paths for imports
sys.path.append('factorgraph')
sys.path.append('factorgraph/application')

from application import *

@dataclass
class AlgorithmRRAMAnalysis:
    """单个算法的RRAM写入分析结果"""
    app_name: str
    algorithm: str
    
    # 因子图结构
    total_variables: int
    total_factors: int
    variable_dimensions: Dict[str, int]
    
    # 基于slam_factorgraph.py的操作分析
    store_operations_writes: int        # Store操作
    writeweights_operations_writes: int # WriteWeights操作
    static_vmm_operations_writes: int   # StaticVMM操作
    factorization_operations_writes: int # Factorization操作
    simd_operations_count: int          # SIMD操作数量(不写RRAM)
    
    # 总计
    total_writes_per_iteration: int
    estimated_iterations: int
    total_writes_complete_solve: int

class Accurate12AlgorithmsAnalyzer:
    """基于实际复杂流程的12个算法RRAM分析器"""
    
    def __init__(self):
        # 基于slam_factorgraph.py的实际参数
        self.slam_params = {
            'rotation_dim': 3,
            'translation_dim': 3,
            'matrix_dim': 3,
            'N_dim': 6,              # Jacobian矩阵行数
            'M_dim': 12,             # Jacobian矩阵列数
        }
        
        # 操作类型的RRAM写入需求 (基于slam_factorgraph.py分析)
        self.operation_rram_requirements = {
            'Store': True,           # 输入数据存储
            'WriteWeights': True,    # 权重写入RRAM
            'StaticVMM': True,       # 静态VMM结果写入
            'Factorization': True,   # QR分解参数写入
            'DynamicVMM': False,     # 动态VMM在SIMD上
            'Transpose': False,      # 转置在SIMD上
            'Preprocessing': False,  # 预处理在SIMD上
            'LOG': False,           # 对数运算在SIMD上
            'EXP': False,           # 指数运算在SIMD上
            'RT': False,            # 旋转转置在SIMD上
            'VP': False,            # 向量运算在SIMD上
            'RV': False,            # 矩阵向量乘在SIMD上
        }
        
        # 不同算法的典型迭代次数
        self.typical_iterations = {
            ('robot', 'localization'): 12,
            ('robot', 'planning'): 15,
            ('robot', 'control'): 8,
            ('manipulator', 'localization'): 10,
            ('manipulator', 'planning'): 20,
            ('manipulator', 'control'): 12,
            ('autovehicle', 'localization'): 15,
            ('autovehicle', 'planning'): 25,
            ('autovehicle', 'control'): 10,
            ('quadrotor', 'localization'): 18,
            ('quadrotor', 'planning'): 30,
            ('quadrotor', 'control'): 15,
        }
    
    def analyze_algorithm(self, app_name: str, algorithm: str) -> AlgorithmRRAMAnalysis:
        """分析单个算法的RRAM写入"""
        
        print(f"\n🔍 分析 {app_name} - {algorithm}")
        
        # 获取因子图结构
        nodes, factors, _ = self._get_factor_graph(app_name, algorithm)
        
        # 分析变量维度
        var_dims = self._analyze_variable_dimensions(nodes, app_name, algorithm)
        total_vars = sum(len(var_list) for var_list in nodes.values())
        total_factors = len(factors)
        
        print(f"  因子图结构: {total_vars} 变量, {total_factors} 因子")
        
        # 基于slam_factorgraph.py流程分析RRAM写入
        store_writes = self._calculate_store_operations(nodes, factors)
        weights_writes = self._calculate_writeweights_operations(nodes, factors)
        static_vmm_writes = self._calculate_static_vmm_operations(nodes, factors)
        factorization_writes = self._calculate_factorization_operations(nodes, factors)
        simd_ops = self._calculate_simd_operations(nodes, factors)
        
        # 每次迭代总写入
        total_per_iter = store_writes + weights_writes + static_vmm_writes + factorization_writes
        
        # 估算迭代次数
        iterations = self.typical_iterations.get((app_name, algorithm), 15)
        total_complete = total_per_iter * iterations
        
        print(f"  Store操作: {store_writes} 次写入")
        print(f"  WriteWeights操作: {weights_writes} 次写入")
        print(f"  StaticVMM操作: {static_vmm_writes} 次写入")
        print(f"  Factorization操作: {factorization_writes} 次写入")
        print(f"  SIMD操作: {simd_ops} 次 (无RRAM写入)")
        print(f"  每次迭代: {total_per_iter} 次写入")
        print(f"  完整求解({iterations}次迭代): {total_complete:,} 次写入")
        
        return AlgorithmRRAMAnalysis(
            app_name=app_name,
            algorithm=algorithm,
            total_variables=total_vars,
            total_factors=total_factors,
            variable_dimensions=var_dims,
            store_operations_writes=store_writes,
            writeweights_operations_writes=weights_writes,
            static_vmm_operations_writes=static_vmm_writes,
            factorization_operations_writes=factorization_writes,
            simd_operations_count=simd_ops,
            total_writes_per_iteration=total_per_iter,
            estimated_iterations=iterations,
            total_writes_complete_solve=total_complete
        )
    
    def _get_factor_graph(self, app_name: str, algorithm: str) -> Tuple[Dict, Dict, str]:
        """获取因子图结构"""
        
        generators = {
            ('robot', 'localization'): gen_robot_localization,
            ('robot', 'planning'): gen_robot_planning,
            ('robot', 'control'): gen_robot_control,
            ('manipulator', 'localization'): gen_manipulator_localization,
            ('manipulator', 'planning'): gen_manipulator_planning,
            ('manipulator', 'control'): gen_manipulator_control,
            ('autovehicle', 'localization'): gen_autovehicle_localization,
            ('autovehicle', 'planning'): gen_autovehicle_planning,
            ('autovehicle', 'control'): gen_autovehicle_control,
            ('quadrotor', 'localization'): gen_quadrotor_localization,
            ('quadrotor', 'planning'): gen_quadrotor_planning,
            ('quadrotor', 'control'): gen_quadrotor_control,
        }
        
        generator = generators.get((app_name, algorithm))
        if not generator:
            raise ValueError(f"未知算法: {app_name} - {algorithm}")
            
        return generator()
    
    def _analyze_variable_dimensions(self, nodes: Dict, app_name: str, algorithm: str) -> Dict[str, int]:
        """分析变量维度"""
        
        var_dims = {}
        
        # 根据应用类型确定变量维度
        if 'poses' in nodes:
            if app_name in ['robot', 'autovehicle']:
                var_dims['poses'] = 3  # 2D位姿 (x, y, θ)
            else:  # manipulator, quadrotor
                var_dims['poses'] = 6  # 3D位姿 (x, y, z, roll, pitch, yaw)
        
        if 'landmarks' in nodes:
            if app_name in ['robot', 'autovehicle']:
                var_dims['landmarks'] = 2  # 2D路标
            else:
                var_dims['landmarks'] = 3  # 3D路标
        
        # 其他变量类型
        for var_type in nodes.keys():
            if var_type not in var_dims:
                if 'waypoint' in var_type or 'path' in var_type:
                    var_dims[var_type] = 3
                elif 'control' in var_type or 'input' in var_type:
                    var_dims[var_type] = 3
                elif 'state' in var_type:
                    var_dims[var_type] = 6
                else:
                    var_dims[var_type] = 3  # 默认
        
        return var_dims
    
    def _calculate_store_operations(self, nodes: Dict, factors: Dict) -> int:
        """计算Store操作的RRAM写入"""
        
        # 基于slam_factorgraph.py的Store操作
        store_operations = [
            (3, 3),  # deltaRij: 3x3
            (3, 1),  # deltaTij: 3x1
            (3, 1),  # ti: 3x1
            (3, 1),  # tj: 3x1
            (3, 1),  # phii: 3x1
            (3, 1),  # phij: 3x1
        ]
        
        total_writes = 0
        for dims in store_operations:
            total_writes += dims[0] * dims[1]
        
        # 根据因子数量缩放
        factor_scale = len(factors) / 10  # 基准10个因子
        return int(total_writes * factor_scale)
    
    def _calculate_writeweights_operations(self, nodes: Dict, factors: Dict) -> int:
        """计算WriteWeights操作的RRAM写入"""
        
        # 基于slam_factorgraph.py的WriteWeights操作
        weight_operations = [
            (9, 3),  # skew_config_matrix: 9x3
            (6, 6),  # omega_matrix: 6x6
        ]
        
        total_writes = 0
        for dims in weight_operations:
            total_writes += dims[0] * dims[1]
        
        # 根据因子数量缩放
        factor_scale = len(factors) / 10
        return int(total_writes * factor_scale)
    
    def _calculate_static_vmm_operations(self, nodes: Dict, factors: Dict) -> int:
        """计算StaticVMM操作的RRAM写入"""
        
        # 基于slam_factorgraph.py的StaticVMM操作
        static_vmm_operations = [
            (3, 3),   # skew_ti_minus_tj: 3x3
            (6, 1),   # weighted_error: 6x1
            (6, 12),  # weighted_jacobian: 6x12
        ]
        
        total_writes = 0
        for dims in static_vmm_operations:
            total_writes += dims[0] * dims[1]
        
        # 根据因子数量缩放
        factor_scale = len(factors) / 10
        return int(total_writes * factor_scale)
    
    def _calculate_factorization_operations(self, nodes: Dict, factors: Dict) -> int:
        """计算Factorization操作的RRAM写入 (QR分解)"""
        
        # 估算Givens旋转次数
        N = self.slam_params['N_dim']  # 6
        M = self.slam_params['M_dim']  # 12
        
        total_rotations = 0
        for col in range(min(N-1, M)):
            for target_row in range(col + 1, N):
                total_rotations += 1
        
        # 每个旋转需要存储2个参数 (c, s)
        factorization_writes = total_rotations * 2
        
        # 根据问题规模缩放
        total_vars = sum(len(var_list) for var_list in nodes.values())
        scale_factor = total_vars / 20  # 基准20个变量
        
        return int(factorization_writes * scale_factor)
    
    def _calculate_simd_operations(self, nodes: Dict, factors: Dict) -> int:
        """计算SIMD操作数量 (不需要RRAM写入)"""
        
        # 基于slam_factorgraph.py的SIMD操作
        simd_operations = [
            'LOG', 'EXP', 'RT', 'DynamicVMM', 'VP', 'RV', 'Transpose', 'Preprocessing'
        ]
        
        # 估算每种操作的数量
        total_simd_ops = len(simd_operations) * len(factors) * 2
        
        return total_simd_ops

    def analyze_all_12_algorithms(self) -> List[AlgorithmRRAMAnalysis]:
        """分析所有12个算法"""

        algorithms = [
            ('robot', 'localization'),
            ('robot', 'planning'),
            ('robot', 'control'),
            ('manipulator', 'localization'),
            ('manipulator', 'planning'),
            ('manipulator', 'control'),
            ('autovehicle', 'localization'),
            ('autovehicle', 'planning'),
            ('autovehicle', 'control'),
            ('quadrotor', 'localization'),
            ('quadrotor', 'planning'),
            ('quadrotor', 'control'),
        ]

        print("🚀 基于slam_factorgraph.py复杂流程的12个算法RRAM写入分析")
        print("=" * 80)
        print("考虑实际操作类型分类和硬件特性")

        results = []

        for app_name, algorithm in algorithms:
            try:
                analysis = self.analyze_algorithm(app_name, algorithm)
                results.append(analysis)
            except Exception as e:
                print(f"❌ 分析错误 {app_name} - {algorithm}: {e}")

        return results

    def print_comprehensive_summary(self, results: List[AlgorithmRRAMAnalysis]):
        """打印综合分析总结"""

        print(f"\n{'='*100}")
        print("📊 12个算法RRAM写入分析总结")
        print(f"{'='*100}")

        print(f"{'算法':<25} {'变量':<8} {'因子':<8} {'每次迭代':<12} {'迭代次数':<10} {'总写入次数':<15}")
        print("-" * 100)

        total_all_writes = 0

        for result in results:
            app_full = f"{result.app_name}_{result.algorithm}"

            print(f"{app_full:<25} {result.total_variables:<8} {result.total_factors:<8} "
                  f"{result.total_writes_per_iteration:<12} {result.estimated_iterations:<10} "
                  f"{result.total_writes_complete_solve:<15,}")

            total_all_writes += result.total_writes_complete_solve

        print("-" * 100)
        print(f"{'总计':<25} {'':<8} {'':<8} {'':<12} {'':<10} {total_all_writes:<15,}")
        print(f"{'平均':<25} {'':<8} {'':<8} {'':<12} {'':<10} {total_all_writes//len(results):<15,}")

        # 按应用分类统计
        print(f"\n📊 按应用分类统计:")
        app_stats = {}
        for result in results:
            if result.app_name not in app_stats:
                app_stats[result.app_name] = []
            app_stats[result.app_name].append(result)

        for app_name, app_results in app_stats.items():
            total_app_writes = sum(r.total_writes_complete_solve for r in app_results)
            avg_app_writes = total_app_writes // len(app_results)
            print(f"  {app_name}: {total_app_writes:,} 总写入, {avg_app_writes:,} 平均")

        # 按算法类型统计
        print(f"\n📊 按算法类型统计:")
        algo_stats = {}
        for result in results:
            if result.algorithm not in algo_stats:
                algo_stats[result.algorithm] = []
            algo_stats[result.algorithm].append(result)

        for algo_type, algo_results in algo_stats.items():
            total_algo_writes = sum(r.total_writes_complete_solve for r in algo_results)
            avg_algo_writes = total_algo_writes // len(algo_results)
            print(f"  {algo_type}: {total_algo_writes:,} 总写入, {avg_algo_writes:,} 平均")

        # 操作类型分析
        print(f"\n📊 操作类型分析 (平均值):")
        avg_store = sum(r.store_operations_writes for r in results) // len(results)
        avg_weights = sum(r.writeweights_operations_writes for r in results) // len(results)
        avg_static_vmm = sum(r.static_vmm_operations_writes for r in results) // len(results)
        avg_factorization = sum(r.factorization_operations_writes for r in results) // len(results)
        avg_simd = sum(r.simd_operations_count for r in results) // len(results)

        print(f"  Store操作: {avg_store} 次RRAM写入")
        print(f"  WriteWeights操作: {avg_weights} 次RRAM写入")
        print(f"  StaticVMM操作: {avg_static_vmm} 次RRAM写入")
        print(f"  Factorization操作: {avg_factorization} 次RRAM写入")
        print(f"  SIMD操作: {avg_simd} 次 (无RRAM写入)")

        total_rram_ops = avg_store + avg_weights + avg_static_vmm + avg_factorization
        print(f"\n🎯 关键发现:")
        print(f"  RRAM写入操作: {total_rram_ops} 次/迭代")
        print(f"  SIMD计算操作: {avg_simd} 次/迭代")
        print(f"  RRAM写入比例: {total_rram_ops/(total_rram_ops+avg_simd)*100:.1f}%")
        print(f"  硬件效率: 大部分计算在SIMD上完成，RRAM主要用于关键数据存储")

    def save_results_json(self, results: List[AlgorithmRRAMAnalysis]):
        """保存结果到JSON文件"""

        data = []
        for result in results:
            data.append({
                'app_name': result.app_name,
                'algorithm': result.algorithm,
                'total_variables': result.total_variables,
                'total_factors': result.total_factors,
                'variable_dimensions': result.variable_dimensions,
                'store_operations_writes': result.store_operations_writes,
                'writeweights_operations_writes': result.writeweights_operations_writes,
                'static_vmm_operations_writes': result.static_vmm_operations_writes,
                'factorization_operations_writes': result.factorization_operations_writes,
                'simd_operations_count': result.simd_operations_count,
                'total_writes_per_iteration': result.total_writes_per_iteration,
                'estimated_iterations': result.estimated_iterations,
                'total_writes_complete_solve': result.total_writes_complete_solve
            })

        with open('accurate_12_algorithms_rram_analysis.json', 'w') as f:
            json.dump(data, f, indent=2)

        print(f"\n💾 结果已保存到 accurate_12_algorithms_rram_analysis.json")


def main():
    """主函数"""

    analyzer = Accurate12AlgorithmsAnalyzer()
    results = analyzer.analyze_all_12_algorithms()
    analyzer.print_comprehensive_summary(results)
    analyzer.save_results_json(results)

    print(f"\n🎉 12个算法的准确RRAM写入分析完成！")
    print(f"📋 基于slam_factorgraph.py的实际复杂操作流程")
    print(f"🔧 考虑了StaticVMM vs DynamicVMM vs SIMD的硬件分工")

    return results


if __name__ == "__main__":
    results = main()
